# WebSocket音频调试指南

## 🔍 当前问题

- ✅ WebSocket连接正常（Wireshark确认）
- ✅ 服务器发送数据正常
- ❌ ESP32没有音频输出

## 🔧 已添加的调试功能

### 1. 详细的回调日志
现在`OnWsAudioData`函数会输出详细信息：
```
I (xxxxx) WsAudio: === CALLBACK TRIGGERED #1 ===
I (xxxxx) WsAudio: Received data: len=70, ptr=0x3fcxxxxx
I (xxxxx) WsAudio: Raw data: 01 01 00 01 3E 00 00 00
I (xxxxx) WsAudio: Packet header: ver=1, type=1, seq=1, payloadLen=62
I (xxxxx) WsAudio: Calling SkOpusDecPlayRemote with 62 bytes...
I (xxxxx) WsAudio: ✅ Audio decode SUCCESS: seq=1, len=62
I (xxxxx) WsAudio: === CALLBACK END #1 ===
```

### 2. 回调注册确认
```
I (xxxxx) SmartKid: Registering WebSocket binary data callback...
I (xxxxx) SmartKid: WebSocket binary data callback registered successfully
```

## 📋 调试步骤

### 步骤1: 编译并测试
```bash
cd /opt/Amor/work/sk-terminal
idf.py build flash monitor
```

### 步骤2: 启动音频服务器
```bash
python3 websocket_audio_server.py your_audio.wav
```

### 步骤3: 观察日志输出

#### 场景A: 回调未被触发
如果看不到 `=== CALLBACK TRIGGERED ===` 日志：
- **问题**: WebSocket二进制数据回调没有工作
- **原因**: WebSocket模块可能没有正确处理二进制帧
- **解决**: 需要检查sk_websocket.c的实现

#### 场景B: 回调被触发但数据格式错误
如果看到回调日志但数据格式不对：
```
I (xxxxx) WsAudio: Invalid packet format: ver=252, type=0
```
- **问题**: 服务器发送的数据格式不正确
- **解决**: 检查服务器端的数据包构造

#### 场景C: 回调正常但解码失败
如果看到：
```
I (xxxxx) WsAudio: ❌ Audio decode FAILED: seq=1, len=62, ret=-1
```
- **问题**: Opus解码器无法处理数据
- **原因**: 服务器发送的不是有效的Opus数据
- **解决**: 需要使用真正的Opus编码器

#### 场景D: 解码成功但无声音
如果看到：
```
I (xxxxx) WsAudio: ✅ Audio decode SUCCESS: seq=1, len=62
```
但仍然没有声音：
- **问题**: 音频播放系统问题
- **解决**: 检查音频输出配置

## 🔧 可能的解决方案

### 解决方案1: 检查WebSocket二进制数据处理

如果回调没有被触发，需要检查`sk_websocket.c`中的二进制数据处理：

```c
// 在sk_websocket.c中查找类似的代码
if (frame_type == WS_FRAME_TYPE_BINARY) {
    if (binDataCallback) {
        binDataCallback(binDataArg, payload, payload_len);
    }
}
```

### 解决方案2: 修复服务器端数据格式

如果数据格式错误，修改服务器端：

```python
# 确保使用小端序
header = struct.pack('<BBHHH', 
                   version,        # 0x01
                   audio_type,     # 0x01
                   seq_num,        # 序列号
                   payload_len,    # 数据长度
                   0)              # 保留字段
```

### 解决方案3: 使用真正的Opus编码

当前服务器使用"伪Opus"数据，需要真正的Opus编码：

```python
# 安装opuslib
pip install opuslib

# 使用真正的Opus编码器
import opuslib
encoder = opuslib.Encoder(16000, 1, opuslib.APPLICATION_AUDIO)
opus_data = encoder.encode(pcm_data, 320)
```

### 解决方案4: 简化测试数据

为了快速验证，可以发送简单的测试数据：

```python
def create_test_packet(self):
    """创建简单的测试包"""
    # 发送固定的测试数据
    test_data = b'\x01\x01\x00\x01\x04\x00\x00\x00\xAA\xBB\xCC\xDD'
    return test_data
```

## 📊 预期的正常日志

正常工作时应该看到：

```
I (18230) SmartKid: Registering WebSocket binary data callback...
I (18230) SmartKid: WebSocket binary data callback registered successfully
I (18230) SmartKid: Auto-starting WebSocket connection...
I (18230) SmartKid: WebSocket connection started after WiFi ready

# 服务器连接后
I (25000) WsAudio: === CALLBACK TRIGGERED #1 ===
I (25000) WsAudio: Received data: len=70, ptr=0x3fc12345
I (25000) WsAudio: Raw data: 01 01 00 01 3E 00 00 00
I (25000) WsAudio: Packet header: ver=1, type=1, seq=1, payloadLen=62
I (25000) WsAudio: Calling SkOpusDecPlayRemote with 62 bytes...
I (25000) WsAudio: ✅ Audio decode SUCCESS: seq=1, len=62
I (25000) WsAudio: === CALLBACK END #1 ===

I (25020) WsAudio: === CALLBACK TRIGGERED #2 ===
I (25020) WsAudio: ✅ Audio decode SUCCESS: seq=2, len=64
I (25020) WsAudio: === CALLBACK END #2 ===
```

## 🎯 下一步行动

1. **立即测试**: 编译并运行，观察是否有回调日志
2. **根据日志判断**: 确定是哪个环节的问题
3. **针对性修复**: 根据具体问题选择对应的解决方案

请运行测试并告诉我看到了什么日志输出！
