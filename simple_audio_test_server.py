#!/usr/bin/env python3
"""
简化的WebSocket音频测试服务器
专门用于调试ESP32音频播放问题
"""

import asyncio
import websockets
import struct
import time

class SimpleAudioTestServer:
    def __init__(self):
        self.seq_num = 0
        
    async def handle_client(self, websocket, path):
        client_addr = websocket.remote_address
        print(f"[{time.strftime('%H:%M:%S')}] Client connected: {client_addr}")
        
        try:
            while True:
                # 创建简单的测试音频包
                test_packet = self.create_test_packet()
                
                # 发送数据
                await websocket.send(test_packet)
                
                self.seq_num += 1
                
                # 每50个包打印一次状态
                if self.seq_num % 50 == 0:
                    print(f"[{time.strftime('%H:%M:%S')}] Sent {self.seq_num} test packets")
                
                # 20ms间隔
                await asyncio.sleep(0.02)
                
        except websockets.exceptions.ConnectionClosed:
            print(f"[{time.strftime('%H:%M:%S')}] Client disconnected: {client_addr}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] Error: {e}")
    
    def create_test_packet(self):
        """创建简单的测试音频包"""
        # 创建简单的测试音频数据
        test_audio_data = self.create_test_audio_data()
        
        # WebSocket音频包头
        version = 0x01
        audio_type = 0x01
        payload_len = len(test_audio_data)
        reserved = 0x00
        
        # 打包头部（小端序）
        header = struct.pack('<BBHHH', 
                           version,                    # version
                           audio_type,                 # type
                           self.seq_num & 0xFFFF,      # seqNum
                           payload_len,                # payloadLen
                           reserved)                   # resv
        
        packet = header + test_audio_data
        
        # 调试信息
        if self.seq_num % 100 == 0:
            print(f"Creating packet #{self.seq_num}: header_len={len(header)}, data_len={len(test_audio_data)}, total_len={len(packet)}")
        
        return packet
    
    def create_test_audio_data(self):
        """创建测试音频数据"""
        # 创建简单的1kHz正弦波PCM数据

        # 320个采样点 = 16kHz * 0.02s (20ms)
        samples = 320
        audio_data = bytearray(samples * 2)  # 16-bit samples

        # 生成1kHz正弦波
        import math
        frequency = 1000  # 1kHz
        sample_rate = 16000
        amplitude = 4000  # 16-bit amplitude (降低音量)

        for i in range(samples):
            # 计算正弦波值
            t = i / sample_rate
            angle = 2 * math.pi * frequency * t
            value = int(amplitude * math.sin(angle))

            # 确保值在16-bit范围内
            value = max(-32768, min(32767, value))

            # 转换为16-bit little-endian
            if value < 0:
                value = 65536 + value  # 转换为无符号表示

            audio_data[i*2] = value & 0xFF
            audio_data[i*2+1] = (value >> 8) & 0xFF

        # 调试信息
        if self.seq_num % 100 == 0:
            print(f"Generated {samples} samples, {len(audio_data)} bytes PCM data")
            print(f"First few samples: {audio_data[0]:02X} {audio_data[1]:02X} {audio_data[2]:02X} {audio_data[3]:02X}")

        return bytes(audio_data)
    
    def create_opus_test_data(self):
        """创建模拟的Opus测试数据"""
        # 创建固定的测试数据，便于调试
        opus_data = bytearray(60)
        
        # Opus帧头（模拟）
        opus_data[0] = 0xFC  # TOC byte
        opus_data[1] = 0x00  # 配置
        
        # 填充测试数据
        for i in range(2, 60):
            opus_data[i] = (self.seq_num + i) & 0xFF
        
        return bytes(opus_data)

async def main():
    server = SimpleAudioTestServer()
    
    host = "0.0.0.0"
    port = 8766
    
    print("=" * 60)
    print("简化WebSocket音频测试服务器")
    print(f"监听地址: ws://{host}:{port}")
    print(f"启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("发送模式: 简单PCM测试数据")
    print("=" * 60)
    print("等待ESP32客户端连接...")
    print("按 Ctrl+C 停止服务器")
    print()
    
    try:
        async with websockets.serve(server.handle_client, host, port):
            await asyncio.Future()
    except KeyboardInterrupt:
        print("\n服务器停止")
    except Exception as e:
        print(f"服务器错误: {e}")

if __name__ == "__main__":
    try:
        import websockets
    except ImportError:
        print("错误: 需要安装websockets库")
        print("请运行: pip install websockets")
        exit(1)
    
    print("简化测试服务器 - 用于调试ESP32音频播放")
    print("发送简单的PCM数据而不是Opus数据")
    print()
    
    asyncio.run(main())
