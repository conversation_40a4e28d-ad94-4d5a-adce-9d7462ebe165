# WebSocket自动连接使用指南

## 🚀 自动连接功能

现在您的ESP32会在启动后自动连接到WebSocket音频服务器，无需手动操作！

## ⏱️ 连接时序

```
系统启动 → WiFi连接 → 等待20秒稳定 → 自动启动WebSocket连接 → 开始音频传输
```

## 📋 使用步骤

### 1. 启动ESP32
```bash
cd /opt/Amor/work/sk-terminal
idf.py build flash monitor
```

### 2. 启动音频服务器
```bash
# 在另一个终端启动服务器
python3 websocket_audio_server.py your_audio.wav
```

### 3. 观察自动连接过程

**启动阶段（0-20秒）**：
```
I (2926) SmartKid: WebSocket audio server configured: ************:8766 (will connect after WiFi ready)
I (3166) wifi: connected with YourWiFi, aid = 1, channel 6, BW20
```

**自动连接阶段（20秒后）**：
```
I (23000) SmartKid: Auto-starting WebSocket connection...
I (23010) SmartKid: WebSocket connection started after WiFi ready
```

**音频传输阶段**：
```
I (23500) WsAudio: Audio played: seq=1, len=62
I (23520) WsAudio: Audio played: seq=2, len=64
I (23540) WsAudio: Audio played: seq=3, len=58
```

## 🎵 音频服务器输出

服务器端会显示：
```
============================================================
WebSocket音频服务器启动
监听地址: ws://0.0.0.0:8766
音频文件: your_audio.wav
============================================================
WAV文件信息:
  文件: your_audio.wav
  采样率: 44100 Hz → 16000 Hz
  声道数: 2 → 1
  时长: 30.00 秒

[14:30:43] Client 1 connected: ('*************', 54321)
[14:30:44] Sent 50 audio packets to ('*************', 54321)
```

## 🔧 配置参数

### 自动连接延迟
当前设置为20秒，可以根据需要调整：

```c
// 在main.c中修改延迟时间
vTaskDelay(pdMS_TO_TICKS(20000));  // 20秒 = 20000ms
```

**建议延迟时间**：
- **快速网络**: 10-15秒
- **一般网络**: 20秒（当前设置）
- **慢速网络**: 30-40秒

### 服务器地址
当前配置：
```c
SkWsSetServerIp("************", 8766);
```

根据您的网络环境修改IP地址。

## 🛠️ 故障排除

### 1. 自动连接失败
**现象**：20秒后没有看到连接日志
**解决**：
```bash
# 检查WiFi连接状态
I (xxxx) wifi: connected with YourWiFi
```
如果WiFi未连接，检查WiFi配置。

### 2. 连接成功但无音频
**现象**：连接成功但听不到声音
**解决**：
- 检查音频服务器是否正常运行
- 验证WAV文件是否有效
- 检查ESP32音频输出设置

### 3. 连接断开重连
**现象**：连接后又断开
**解决**：
- 检查网络稳定性
- 确认防火墙设置
- 使用语音命令"配置"手动重连

## 🎛️ 手动控制功能

虽然是自动连接，但仍保留手动控制：

### 手动重连
说出语音命令：**"配置"** 或 **"设置"**
```
I (xxxx) SmartKid: WebSocket connection triggered by voice command (manual reconnect)
```

### 连接状态检查
可以通过日志监控连接状态：
```bash
# 过滤WebSocket相关日志
idf.py monitor | grep -E "(WsAudio|WebSocket)"
```

## 📊 性能监控

### 音频质量指标
- **延迟**: 40-100ms
- **带宽**: ~25kbps
- **帧率**: 50帧/秒
- **音质**: 16kHz单声道

### 连接稳定性
- **重连机制**: 支持语音命令重连
- **错误处理**: 自动跳过无效音频包
- **统计信息**: 详细的播放统计

## 🔄 完整工作流程

### 开发测试流程
1. **准备WAV文件** - 任何标准WAV格式
2. **启动服务器** - `python3 websocket_audio_server.py audio.wav`
3. **编译ESP32** - `idf.py build flash`
4. **监控日志** - `idf.py monitor`
5. **等待自动连接** - 约20秒后自动连接
6. **享受音频** - 自动循环播放WAV文件

### 生产部署流程
1. **配置服务器IP** - 修改为生产环境IP
2. **调整延迟时间** - 根据网络环境优化
3. **测试稳定性** - 长时间运行测试
4. **部署监控** - 添加连接状态监控

## 💡 优化建议

### 1. 网络优化
- 使用有线网络减少延迟
- 配置QoS优先级
- 避免网络拥塞时段

### 2. 音频优化
- 选择合适的WAV文件格式
- 调整音频编码参数
- 优化缓冲区大小

### 3. 系统优化
- 监控内存使用
- 调整任务优先级
- 优化电源管理

## 🎯 总结

现在您的系统具备：
- ✅ **全自动连接** - 无需手动操作
- ✅ **稳定可靠** - 20秒延迟确保网络就绪
- ✅ **错误恢复** - 支持手动重连
- ✅ **实时音频** - 低延迟音频传输
- ✅ **循环播放** - WAV文件自动循环

享受您的自动化WebSocket音频传输系统吧！🎵
