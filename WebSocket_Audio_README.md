# WebSocket音频传输极简实现

## 概述

这是一个极简的WebSocket音频传输实现，只需要在现有代码中添加约30行代码即可实现从WebSocket服务器到ESP32的实时音频传输。

## 实现特点

- ✅ **极简设计**: 只需30行代码，无需额外文件
- ✅ **零侵入**: 完全不修改现有protocol模块
- ✅ **高复用**: 100%复用现有的Opus解码和音频播放系统
- ✅ **易理解**: 代码逻辑清晰，一目了然
- ✅ **易维护**: 没有复杂的抽象层

## 代码修改

### 已修改的文件

1. **main/app/main.c** - 添加了WebSocket音频处理功能

### 新增内容

1. **WebSocket音频包结构定义** (8行)
2. **音频数据回调函数** (20行)  
3. **回调注册和服务器配置** (4行)

## 数据格式

### WebSocket音频包格式

```c
typedef struct __attribute__((packed)) {
    uint8_t version;        // 0x01 (版本号)
    uint8_t type;           // 0x01 (音频类型)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // Opus数据长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus音频数据
} WsAudioPacket;
```

### 数据流程

```
WebSocket服务器 -> ESP32客户端 -> OnWsAudioData() -> SkOpusDecPlayRemote() -> 音频播放
```

## 使用方法

### 1. 编译ESP32代码

```bash
cd /opt/Amor/work/sk-terminal
idf.py build
idf.py flash monitor
```

### 2. 启动WebSocket音频服务器

```bash
# 安装依赖
pip install websockets

# 启动服务器
python3 websocket_audio_server.py
```

### 3. 配置网络

确保ESP32和服务器在同一网络中，并修改main.c中的服务器IP地址：

```c
// 修改为实际的服务器IP地址
SkWsSetServerIp("*************", 8080);
```

### 4. 测试连接

1. ESP32启动后会自动连接到WebSocket服务器
2. 服务器开始发送模拟音频数据
3. ESP32接收并播放音频

## 日志监控

### ESP32端日志

```
I (12345) WsAudio: Audio played: seq=123, len=64
I (12365) WsAudio: Audio played: seq=124, len=68
```

### 服务器端日志

```
[14:30:15] Client 1 connected: ('*************', 54321)
[14:30:16] Sent 50 audio packets to ('*************', 54321)
```

## 错误处理

### 常见错误及解决方案

1. **连接失败**
   ```
   E (12345) WsAudio: Connection failed
   ```
   - 检查服务器IP和端口配置
   - 确认WiFi连接正常
   - 验证防火墙设置

2. **数据格式错误**
   ```
   E (12345) WsAudio: Invalid packet: len=0, ver=0, type=0
   ```
   - 检查服务器发送的数据格式
   - 验证字节序（小端序）

3. **解码失败**
   ```
   E (12345) WsAudio: Decode failed: seq=123, len=64, ret=-1
   ```
   - 检查Opus数据是否有效
   - 验证采样率和声道配置

## 性能参数

- **音频格式**: Opus编码，16kHz，单声道
- **传输频率**: 20ms一帧（50帧/秒）
- **延迟**: < 100ms（网络延迟 + 解码延迟）
- **带宽**: 约32kbps（取决于Opus编码参数）

## 扩展功能

### 1. 真实音频数据

替换服务器端的模拟数据为真实的Opus编码音频：

```python
def get_opus_audio_data(self):
    # 从麦克风或音频文件获取PCM数据
    pcm_data = get_pcm_from_source()
    
    # 使用Opus编码器编码
    opus_data = opus_encoder.encode(pcm_data)
    
    return opus_data
```

### 2. 音频质量控制

```python
# 不同质量的音频参数
AUDIO_QUALITY = {
    'low': {'bitrate': 16000, 'frame_size': 20},
    'medium': {'bitrate': 32000, 'frame_size': 20},
    'high': {'bitrate': 64000, 'frame_size': 10}
}
```

### 3. 多客户端支持

服务器已支持多客户端连接，每个ESP32设备可以独立接收音频流。

## 故障排除

### 调试步骤

1. **检查网络连接**
   ```bash
   ping *************  # 从ESP32网络ping服务器
   ```

2. **监控WebSocket连接**
   ```c
   SK_LOGI(TAG, "WebSocket connected: %s", SkWsIsConnected() ? "Yes" : "No");
   ```

3. **检查音频数据**
   ```c
   SK_LOGI(TAG, "Received packet: seq=%d, len=%d", pkt->seqNum, pkt->payloadLen);
   ```

### 性能优化

1. **网络优化**
   - 使用有线网络减少延迟
   - 调整WiFi信道避免干扰

2. **音频优化**
   - 调整Opus编码参数
   - 优化音频缓冲区大小

## 总结

这个极简实现完美地展示了如何在不破坏现有架构的前提下，用最少的代码实现复杂的功能。它充分利用了现有的protocol设计优势，是一个优雅的解决方案。
