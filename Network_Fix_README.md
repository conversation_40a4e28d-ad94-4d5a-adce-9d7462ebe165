# 网络初始化问题修复说明

## 问题描述

遇到的错误：
```
assert failed: tcpip_send_msg_wait_sem /IDF/components/lwip/lwip/src/api/tcpip.c:454 (Invalid mbox)
```

## 问题原因

**根本原因**：在WiFi网络栈完全初始化之前就尝试进行WebSocket连接，导致lwip断言失败。

**具体分析**：
1. `app_main()`中的初始化顺序问题
2. `SkWsStartConnect()`在WiFi连接完成前被调用
3. 网络栈未准备好时进行网络操作

## 修复方案

### 1. 移除立即连接
```c
// 修改前（有问题）
SkWsSetServerIp("************", 8766);
SkWsStartConnect();  // ❌ 立即连接，WiFi可能未准备好

// 修改后（正确）
SkWsSetServerIp("************", 8766);
// 不立即连接，等待合适时机
```

### 2. 添加延迟连接函数
```c
// 新增函数：在合适时机启动WebSocket连接
void StartWebSocketConnection(void) {
    static bool ws_connected = false;
    
    if (!ws_connected) {
        SkWsStartConnect();
        ws_connected = true;
        SK_LOGI(TAG, "WebSocket connection started after WiFi ready");
    }
}
```

### 3. 语音命令触发连接
```c
// 修改命令处理，支持语音启动WebSocket
void SkMainCmdProc(int32_t cmd) {
    switch (cmd) {
        case SPEECH_CMD_EVENT_CONFIG:  // "配置" 或 "设置"
            StartWebSocketConnection();
            break;
        // ... 其他命令
    }
}
```

## 使用方法

### 自动连接模式（当前实现）
1. 编译并烧录修复后的代码
2. 系统启动后会自动等待20秒让WiFi连接稳定
3. 20秒后自动启动WebSocket连接
4. 无需手动操作，完全自动化

### 手动重连功能（可选）
如果需要重新连接WebSocket，可以：
- 说出语音命令：**"配置"** 或 **"设置"**
- 系统会重新尝试WebSocket连接

### 方法3：WiFi事件回调启动（最佳）
在WiFi连接成功事件中启动WebSocket（需要修改WiFi模块）：

```c
// 在WiFi连接成功回调中添加
void on_wifi_connected(void) {
    // WiFi连接成功后启动WebSocket
    StartWebSocketConnection();
}
```

## 测试步骤

### 1. 编译和烧录
```bash
cd /opt/Amor/work/sk-terminal
idf.py build flash monitor
```

### 2. 观察启动日志
正常启动应该看到：
```
I (xxxx) SmartKid: WebSocket audio server configured: ************:8766 (will connect after WiFi ready)
```

### 3. 自动WebSocket连接
等待约20秒后，应该自动看到：
```
I (xxxx) SmartKid: Auto-starting WebSocket connection...
I (xxxx) SmartKid: WebSocket connection started after WiFi ready
```

### 4. 启动音频服务器
```bash
python3 websocket_audio_server.py your_audio.wav
```

### 5. 验证连接
服务器端应该显示：
```
[14:30:20] Client 1 connected: ('*************', 54321)
```

ESP32端应该显示：
```
I (xxxx) WsAudio: Audio played: seq=1, len=62
```

## 故障排除

### 1. 如果仍然出现网络错误
```bash
# 检查WiFi连接状态
I (xxxx) wifi: connected with YourWiFi, aid = 1, channel 6, BW20, bssid = xx:xx:xx:xx:xx:xx
```

### 2. 如果WebSocket连接失败
- 确认服务器IP地址正确
- 检查防火墙设置
- 验证网络连通性

### 3. 如果语音命令无响应
- 确认语音识别模块正常工作
- 检查麦克风输入
- 尝试其他语音命令

## 技术说明

### 网络初始化时序
```
1. 系统启动
2. WiFi模块初始化
3. WebSocket模块初始化（但不连接）
4. 其他模块初始化
5. WiFi连接建立
6. 网络栈完全准备
7. 触发WebSocket连接（语音命令或延迟）
```

### 安全性考虑
- 避免在网络未准备好时进行网络操作
- 使用静态标志防止重复连接
- 添加错误处理和重试机制

## 总结

这个修复方案通过调整网络连接的时序，避免了在网络栈未完全初始化时进行网络操作，从而解决了lwip断言错误。

现在您可以：
1. 安全地启动系统而不会崩溃
2. 通过语音命令控制WebSocket连接
3. 正常使用WAV文件音频传输功能

修复后的系统更加稳定和可控！
