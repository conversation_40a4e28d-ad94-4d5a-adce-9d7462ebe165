# WebSocket实时音频传输实现指南

## 概述

基于现有sk-terminal项目的protocol架构，实现从WebSocket服务器到ESP32客户端的实时音频传输。本方案**无需修改现有代码**，完全基于现有接口实现。

## 架构设计

```
WebSocket服务器 -> sk_websocket.c -> 音频回调处理 -> SkOpusDecPlayRemote -> 音频播放
                                          ↑
                                    复用现有解码器
```

## 实现步骤

### 1. 头文件定义

#### 1.1 在 `main/include/sk_websocket_audio.h` 中添加：

```c
/**
 * @file: sk_websocket_audio.h
 * @description: WebSocket音频传输扩展定义
 */
#ifndef SK_WEBSOCKET_AUDIO_H
#define SK_WEBSOCKET_AUDIO_H

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"
#include "sk_websocket.h"

#ifdef __cplusplus
extern "C" {
#endif

// WebSocket音频数据类型（复用现有定义）
#define SK_WS_AUDIO_TYPE            SK_WS_DATA_TYPE_AUDIO
#define SK_WS_AUDIO_SESSION_ID      0xFFFF  // WebSocket音频流固定会话ID

// 音频源类型
typedef enum {
    SK_AUDIO_SOURCE_RLINK = 0,      // 现有Rlink音频
    SK_AUDIO_SOURCE_WEBSOCKET = 1,  // WebSocket音频
    SK_AUDIO_SOURCE_BOTH = 2        // 混音模式
} SkAudioSource_t;

// WebSocket音频包结构（基于现有SkWsBinaryHeader_t）
typedef struct __attribute__((packed)) {
    uint8_t version;        // SK_WS_VERSION (0x01)
    uint8_t type;           // SK_WS_DATA_TYPE_AUDIO (0x01)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // Opus数据长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus编码音频数据
} SkWsAudioPacket_t;

// WebSocket音频统计
typedef struct {
    uint32_t wsAudioPacketCnt;      // 接收包计数
    uint32_t wsAudioErrorCnt;       // 错误包计数
    uint32_t wsAudioPlayCnt;        // 播放计数
    uint32_t wsAudioDropCnt;        // 丢包计数
    uint32_t wsLastSeqNum;          // 最后序列号
    uint32_t wsAudioBytes;          // 接收字节数
} SkWsAudioStat_t;

// 函数声明
void SkWsAudioInit(void);
void SkWsAudioDeinit(void);
void SkWsAudioSetSource(SkAudioSource_t source);
SkAudioSource_t SkWsAudioGetSource(void);
void SkWsAudioShowStat(void);
void SkWsAudioResetStat(void);

// WebSocket音频数据回调（内部使用）
void SkWsOnAudioData(void *arg, void *data, uint16_t len);

#ifdef __cplusplus
}
#endif

#endif // SK_WEBSOCKET_AUDIO_H
```

### 1.2 在 `main/include/sk_common.h` 中添加（如果不存在）：

```c
// 在现有定义中添加
#ifndef SK_MIN
#define SK_MIN(a, b) ((a) < (b) ? (a) : (b))
#endif

#ifndef SK_MAX
#define SK_MAX(a, b) ((a) > (b) ? (a) : (b))
#endif
```

### 2. 实现文件

#### 2.1 创建 `main/protocol/sk_websocket_audio.c`：

```c
/**
 * @file: sk_websocket_audio.c
 * @description: WebSocket音频传输实现
 */
#include <string.h>
#include "sk_websocket_audio.h"
#include "sk_websocket.h"
#include "sk_opus_dec.h"
#include "sk_os.h"
#include "sk_log.h"

static const char *TAG = "WsAudio";

// 全局控制结构
typedef struct {
    SkAudioSource_t audioSource;
    SkWsAudioStat_t stat;
    bool initialized;
} SkWsAudioCtrl_t;

static SkWsAudioCtrl_t g_wsAudioCtrl = {
    .audioSource = SK_AUDIO_SOURCE_RLINK,
    .stat = {0},
    .initialized = false
};

/**
 * @brief WebSocket音频数据回调处理
 */
void SkWsOnAudioData(void *arg, void *data, uint16_t len) {
    SkWsAudioPacket_t *audioPacket;
    SkAudioDownlinkTimeRecord timeRecord = {0};
    int32_t ret;
    
    if (!g_wsAudioCtrl.initialized) {
        return;
    }
    
    // 检查数据长度
    if (len < sizeof(SkWsAudioPacket_t)) {
        g_wsAudioCtrl.stat.wsAudioErrorCnt++;
        SK_LOGE(TAG, "Audio packet too short: %d", len);
        return;
    }
    
    audioPacket = (SkWsAudioPacket_t *)data;
    g_wsAudioCtrl.stat.wsAudioPacketCnt++;
    g_wsAudioCtrl.stat.wsAudioBytes += len;
    
    // 验证数据格式
    if (audioPacket->version != SK_WS_VERSION || 
        audioPacket->type != SK_WS_AUDIO_TYPE) {
        g_wsAudioCtrl.stat.wsAudioErrorCnt++;
        SK_LOGE(TAG, "Invalid audio packet: ver=%d, type=%d", 
                audioPacket->version, audioPacket->type);
        return;
    }
    
    // 检查负载长度
    if (audioPacket->payloadLen != (len - sizeof(SkWsAudioPacket_t))) {
        g_wsAudioCtrl.stat.wsAudioErrorCnt++;
        SK_LOGE(TAG, "Payload length mismatch: expected=%d, actual=%d", 
                audioPacket->payloadLen, len - sizeof(SkWsAudioPacket_t));
        return;
    }
    
    // 序列号检查（简单的丢包检测）
    if (g_wsAudioCtrl.stat.wsAudioPacketCnt > 1) {
        uint16_t expectedSeq = g_wsAudioCtrl.stat.wsLastSeqNum + 1;
        if (audioPacket->seqNum != expectedSeq) {
            g_wsAudioCtrl.stat.wsAudioDropCnt++;
            SK_LOGW(TAG, "Sequence gap: expected=%d, got=%d", 
                    expectedSeq, audioPacket->seqNum);
        }
    }
    g_wsAudioCtrl.stat.wsLastSeqNum = audioPacket->seqNum;
    
    // 检查音频源设置
    if (g_wsAudioCtrl.audioSource == SK_AUDIO_SOURCE_RLINK) {
        return; // 当前使用Rlink音频，忽略WebSocket音频
    }
    
    // 构造时间戳记录
    timeRecord.decRxTick = SkOsGetTickCnt();
    timeRecord.relayRxTick = 0;
    timeRecord.relayTxTick = 0;
    timeRecord.encDoneTick = 0;
    timeRecord.encTxTick = 0;
    timeRecord.decStartTick = 0;
    timeRecord.playTick = 0;
    
    // 调用现有的解码播放函数
    ret = SkOpusDecPlayRemote(SkOpusDecGetHandler(), 
                             SK_WS_AUDIO_SESSION_ID,
                             audioPacket->data, 
                             audioPacket->payloadLen, 
                             &timeRecord);
    
    if (ret == SK_RET_SUCCESS) {
        g_wsAudioCtrl.stat.wsAudioPlayCnt++;
    } else {
        g_wsAudioCtrl.stat.wsAudioErrorCnt++;
        SK_LOGE(TAG, "Decode failed: ret=%d", ret);
    }
    
    SK_LOGD(TAG, "Audio packet processed: seq=%d, len=%d, ret=%d", 
            audioPacket->seqNum, audioPacket->payloadLen, ret);
}

/**
 * @brief 初始化WebSocket音频模块
 */
void SkWsAudioInit(void) {
    if (g_wsAudioCtrl.initialized) {
        return;
    }
    
    // 重置统计信息
    memset(&g_wsAudioCtrl.stat, 0, sizeof(SkWsAudioStat_t));
    
    // 注册WebSocket二进制数据回调
    SkWsRegOnBinDataCallback(SkWsOnAudioData, NULL);
    
    g_wsAudioCtrl.initialized = true;
    SK_LOGI(TAG, "WebSocket audio initialized");
}

/**
 * @brief 反初始化WebSocket音频模块
 */
void SkWsAudioDeinit(void) {
    if (!g_wsAudioCtrl.initialized) {
        return;
    }
    
    // 注销回调（设置为NULL）
    SkWsRegOnBinDataCallback(NULL, NULL);
    
    g_wsAudioCtrl.initialized = false;
    SK_LOGI(TAG, "WebSocket audio deinitialized");
}

/**
 * @brief 设置音频源
 */
void SkWsAudioSetSource(SkAudioSource_t source) {
    g_wsAudioCtrl.audioSource = source;
    SK_LOGI(TAG, "Audio source set to: %d", source);
}

/**
 * @brief 获取当前音频源
 */
SkAudioSource_t SkWsAudioGetSource(void) {
    return g_wsAudioCtrl.audioSource;
}

/**
 * @brief 显示WebSocket音频统计信息
 */
void SkWsAudioShowStat(void) {
    SkWsAudioStat_t *stat = &g_wsAudioCtrl.stat;
    
    SK_LOGI(TAG, "=== WebSocket Audio Statistics ===");
    SK_LOGI(TAG, "Packets: %u, Errors: %u, Played: %u, Dropped: %u", 
            stat->wsAudioPacketCnt, stat->wsAudioErrorCnt, 
            stat->wsAudioPlayCnt, stat->wsAudioDropCnt);
    SK_LOGI(TAG, "Bytes: %u, LastSeq: %u, Source: %d", 
            stat->wsAudioBytes, stat->wsLastSeqNum, g_wsAudioCtrl.audioSource);
    
    if (stat->wsAudioPacketCnt > 0) {
        uint32_t errorRate = (stat->wsAudioErrorCnt * 100) / stat->wsAudioPacketCnt;
        uint32_t dropRate = (stat->wsAudioDropCnt * 100) / stat->wsAudioPacketCnt;
        SK_LOGI(TAG, "Error Rate: %u%%, Drop Rate: %u%%", errorRate, dropRate);
    }
}

/**
 * @brief 重置WebSocket音频统计信息
 */
void SkWsAudioResetStat(void) {
    memset(&g_wsAudioCtrl.stat, 0, sizeof(SkWsAudioStat_t));
    SK_LOGI(TAG, "Statistics reset");
}
```

### 3. 构建配置修改

#### 3.1 修改 `main/protocol/CMakeLists.txt`：

```cmake
# 在现有srcs列表中添加
set(srcs
    sk_frame.c
    sk_clink.c
    sk_rlink.c
    sk_audio_buffer.c
    sk_mqtt.c
    sk_websocket.c
    sk_websocket_audio.c    # 新增
    )

idf_component_register(SRCS ${srcs}
                       INCLUDE_DIRS ./
                       )
```

### 4. 主程序集成

#### 4.1 修改 `main/app/main.c`：

在文件顶部添加头文件：
```c
#include "sk_websocket_audio.h"
```

在 `app_main()` 函数中添加初始化代码：
```c
void app_main(void) {
    // ... 现有初始化代码 ...
    
    // WebSocket初始化（现有代码）
    SkWsInit();
    SkWsStart();
    SK_OS_MODULE_MEM_STAT("WebSocket", true);
    
    // 新增：WebSocket音频初始化
    SkWsAudioInit();
    
    // 配置WebSocket音频服务器（根据实际情况修改）
    SkWsSetServerIp("*************", 8080);  // 音频服务器地址
    SkWsStartConnect();  // 连接到音频服务器
    
    SK_OS_MODULE_MEM_STAT("WebSocketAudio", true);
    
    // ... 其余现有代码保持不变 ...
}
```

### 5. 语音命令集成（可选）

#### 5.1 在 `main.c` 的语音命令映射中添加：

```c
SkSpeechMapItem g_skSpeechMap[] = {
    // ... 现有命令 ...
    {SPEECH_CMD_EVENT_WS_AUDIO, "wang luo yin pin"},     // 网络音频
    {SPEECH_CMD_EVENT_LOCAL_AUDIO, "ben di yin pin"},    // 本地音频
    {SPEECH_CMD_EVENT_AUDIO_STAT, "yin pin tong ji"},    // 音频统计
};
```

#### 5.2 在命令处理函数中添加：

```c
void SkMainCmdProc(int32_t cmd) {
    if (g_smHandler == NULL) {
        return;
    }
    
    // 新增音频源切换命令处理
    switch (cmd) {
        case SPEECH_CMD_EVENT_WS_AUDIO:
            SkWsAudioSetSource(SK_AUDIO_SOURCE_WEBSOCKET);
            SK_LOGI(TAG, "Switched to WebSocket audio");
            break;
        case SPEECH_CMD_EVENT_LOCAL_AUDIO:
            SkWsAudioSetSource(SK_AUDIO_SOURCE_RLINK);
            SK_LOGI(TAG, "Switched to local audio");
            break;
        case SPEECH_CMD_EVENT_AUDIO_STAT:
            SkWsAudioShowStat();
            break;
        default:
            SkSmSendEvent(g_smHandler, SM_EVENT_CMD, cmd, 0, 0);
            break;
    }
}
```

## 服务器端实现

### Python WebSocket音频服务器示例

```python
import asyncio
import websockets
import struct
import time

class AudioServer:
    def __init__(self):
        self.seq_num = 0
    
    async def handle_client(self, websocket, path):
        print(f"Client connected: {websocket.remote_address}")
        
        try:
            # 模拟音频流发送
            while True:
                # 模拟获取Opus编码数据（实际应从音频源获取）
                opus_data = self.get_opus_audio_data()
                
                # 构造WebSocket音频包
                packet = self.create_audio_packet(opus_data)
                
                # 发送二进制数据
                await websocket.send(packet)
                
                self.seq_num += 1
                await asyncio.sleep(0.02)  # 20ms间隔
                
        except websockets.exceptions.ConnectionClosed:
            print(f"Client disconnected: {websocket.remote_address}")
    
    def create_audio_packet(self, opus_data):
        """创建WebSocket音频包"""
        version = 0x01
        audio_type = 0x01
        payload_len = len(opus_data)
        reserved = 0x00
        
        # 打包头部（小端序）
        header = struct.pack('<BBHHH', 
                           version,
                           audio_type,
                           self.seq_num & 0xFFFF,
                           payload_len,
                           reserved)
        
        return header + opus_data
    
    def get_opus_audio_data(self):
        """获取Opus编码音频数据（示例）"""
        # 这里应该是实际的Opus编码数据
        # 示例：返回固定长度的模拟数据
        return b'\x00' * 64  # 64字节的模拟Opus数据

# 启动服务器
async def main():
    server = AudioServer()
    
    print("Starting WebSocket audio server on ws://0.0.0.0:8080")
    async with websockets.serve(server.handle_client, "0.0.0.0", 8080):
        await asyncio.Future()  # 永远运行

if __name__ == "__main__":
    asyncio.run(main())
```

## 测试和调试

### 1. 编译测试

```bash
cd /opt/Amor/work/sk-terminal
idf.py build
```

### 2. 运行时测试

1. 启动WebSocket音频服务器
2. ESP32连接WiFi后自动连接到音频服务器
3. 使用语音命令切换音频源：
   - "网络音频" - 切换到WebSocket音频
   - "本地音频" - 切换回Rlink音频
   - "音频统计" - 查看统计信息

### 3. 日志监控

```bash
idf.py monitor
```

关注以下日志：
- `WsAudio: Audio packet processed` - 音频包处理
- `WsAudio: Statistics` - 统计信息
- `SkOpusDec: Play remote` - 解码播放

## 性能优化建议

1. **缓冲区大小**：根据网络延迟调整WebSocket缓冲区
2. **发送频率**：服务器端建议20ms一帧，平衡延迟和效率
3. **错误恢复**：实现丢包重传或FEC机制
4. **自适应码率**：根据网络状况调整音频质量

## 故障排除

### 常见问题及解决方案

1. **连接失败**
   - 检查服务器IP和端口配置
   - 确认WiFi连接正常
   - 验证防火墙设置

2. **音频断续**
   - 检查网络延迟和丢包率
   - 调整发送频率和缓冲区大小
   - 监控CPU使用率

3. **解码失败**
   - 验证Opus数据格式和参数
   - 检查采样率匹配（16kHz）
   - 确认声道数配置（单声道）

4. **内存不足**
   - 监控内存使用情况
   - 调整音频缓冲区大小
   - 检查内存泄漏

### 调试命令

```c
// 在串口中可以使用的调试命令
void debug_ws_audio_status() {
    SkWsAudioShowStat();
    SK_LOGI("Debug", "WebSocket connected: %s",
            SkWsIsConnected() ? "Yes" : "No");
    SK_LOGI("Debug", "Audio source: %d", SkWsAudioGetSource());
}
```

### 性能监控

```c
// 添加到定时器回调中进行周期性监控
void audio_performance_monitor() {
    static uint32_t lastPacketCnt = 0;
    uint32_t currentPacketCnt = g_wsAudioCtrl.stat.wsAudioPacketCnt;
    uint32_t packetsPerSec = currentPacketCnt - lastPacketCnt;

    SK_LOGI("Monitor", "Audio packets/sec: %u", packetsPerSec);
    lastPacketCnt = currentPacketCnt;
}
```

## 扩展功能

### 1. 音频质量自适应

```c
typedef enum {
    WS_AUDIO_QUALITY_LOW = 0,    // 低质量，低带宽
    WS_AUDIO_QUALITY_MEDIUM = 1, // 中等质量
    WS_AUDIO_QUALITY_HIGH = 2    // 高质量，高带宽
} SkWsAudioQuality_t;

void SkWsAudioSetQuality(SkWsAudioQuality_t quality);
```

### 2. 双向音频传输

```c
// 支持向服务器发送音频
int32_t SkWsAudioSendData(uint8_t *data, uint16_t len);

// 音频上传回调
typedef void (*SkWsAudioUploadCallback_t)(uint8_t *data, uint16_t len);
void SkWsAudioRegUploadCallback(SkWsAudioUploadCallback_t callback);
```

### 3. 多路音频流

```c
// 支持多个音频流
typedef struct {
    uint16_t streamId;
    SkWsAudioQuality_t quality;
    bool enabled;
} SkWsAudioStream_t;

int32_t SkWsAudioCreateStream(uint16_t streamId);
int32_t SkWsAudioDestroyStream(uint16_t streamId);
```

## 配置参数

### 编译时配置（sdkconfig）

```
# WebSocket音频配置
CONFIG_WS_AUDIO_ENABLE=y
CONFIG_WS_AUDIO_BUFFER_SIZE=2048
CONFIG_WS_AUDIO_MAX_PACKET_SIZE=1024
CONFIG_WS_AUDIO_TIMEOUT_MS=5000
```

### 运行时配置

```c
typedef struct {
    char serverIp[16];
    uint16_t serverPort;
    uint32_t reconnectInterval;  // 重连间隔(ms)
    uint32_t maxPacketSize;      // 最大包大小
    bool autoReconnect;          // 自动重连
} SkWsAudioConfig_t;

void SkWsAudioSetConfig(const SkWsAudioConfig_t *config);
```

## 总结

这个实现方案的主要优势：

1. **零侵入性**：完全不修改现有代码
2. **高性能**：复用现有的优化机制
3. **易扩展**：模块化设计，便于功能扩展
4. **易调试**：完善的日志和统计信息
5. **易维护**：清晰的接口和文档

通过这个方案，您可以快速实现WebSocket实时音频传输功能，同时保持现有系统的稳定性和性能。
