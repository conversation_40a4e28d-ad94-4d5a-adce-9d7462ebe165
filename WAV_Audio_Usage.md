# WebSocket WAV音频播放使用指南

## 概述

现在您可以使用本地的WAV文件通过WebSocket发送到ESP32进行播放了！

## 快速开始

### 1. 安装依赖

```bash
pip install websockets numpy
```

### 2. 准备WAV文件

支持的WAV文件格式：
- **采样率**: 任意（会自动转换为16kHz）
- **声道**: 单声道或立体声（立体声会转换为单声道）
- **位深**: 8bit、16bit、32bit
- **格式**: 标准WAV文件

### 3. 启动服务器

#### 使用WAV文件：
```bash
python3 websocket_audio_server.py your_audio.wav
```

#### 使用模拟数据（测试用）：
```bash
python3 websocket_audio_server.py
```

### 4. ESP32配置

您的ESP32已经配置为连接到 `************:8766`，确保：
1. ESP32和服务器在同一网络
2. 服务器IP地址正确
3. 防火墙允许8766端口

## 使用示例

### 示例1：播放音乐文件
```bash
python3 websocket_audio_server.py music.wav
```

### 示例2：播放语音文件
```bash
python3 websocket_audio_server.py voice.wav
```

### 示例3：播放任意音频
```bash
python3 websocket_audio_server.py /path/to/your/audio.wav
```

## 服务器输出示例

```
============================================================
WebSocket音频服务器启动
监听地址: ws://0.0.0.0:8766
启动时间: 2025-01-29 14:30:15
音频文件: music.wav
============================================================
WAV文件信息:
  文件: music.wav
  采样率: 44100 Hz
  声道数: 2
  位深: 16 bit
  处理后: 16000 Hz, 单声道, 480000 采样点
  时长: 30.00 秒
等待ESP32客户端连接...
按 Ctrl+C 停止服务器

[14:30:20] Client 1 connected: ('*************', 54321)
[14:30:21] Sent 50 audio packets to ('*************', 54321)
[14:30:22] Sent 100 audio packets to ('*************', 54321)
```

## ESP32端日志示例

```
I (12345) SmartKid: WebSocket audio server configured: ************:8766
I (12350) WsAudio: Audio played: seq=1, len=62
I (12370) WsAudio: Audio played: seq=2, len=62
I (12390) WsAudio: Audio played: seq=3, len=62
```

## 音频处理流程

```
WAV文件 -> 重采样(16kHz) -> 单声道转换 -> 分帧(20ms) -> 伪Opus编码 -> WebSocket -> ESP32
```

## 技术细节

### 音频转换
1. **重采样**: 自动转换为16kHz（ESP32 Opus解码器要求）
2. **声道转换**: 立体声转单声道（取平均值）
3. **分帧**: 每20ms一帧（320个采样点）
4. **编码**: 简化的"伪Opus"编码（实际项目中应使用真正的Opus编码器）

### 数据格式
```c
// ESP32端接收的数据包格式
typedef struct {
    uint8_t version;        // 0x01
    uint8_t type;           // 0x01
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // 音频数据长度
    uint16_t resv;          // 保留
    uint8_t data[];         // 音频数据
} WsAudioPacket;
```

### 性能参数
- **延迟**: ~40-100ms（网络延迟 + 处理延迟）
- **带宽**: ~25kbps（压缩后）
- **帧率**: 50帧/秒（20ms间隔）
- **音质**: 16kHz单声道（电话音质）

## 故障排除

### 1. 连接问题
```
错误: ESP32无法连接到服务器
解决: 
- 检查IP地址是否正确
- 确认防火墙设置
- 验证网络连通性: ping ************
```

### 2. 音频问题
```
错误: ESP32收到数据但无声音
解决:
- 检查ESP32音频输出配置
- 验证Opus解码器初始化
- 查看ESP32端错误日志
```

### 3. 文件格式问题
```
错误: WAV文件加载失败
解决:
- 确认文件是标准WAV格式
- 检查文件路径是否正确
- 尝试使用其他音频文件
```

## 扩展功能

### 1. 真正的Opus编码
```python
# 安装 opuslib
pip install opuslib

# 在代码中使用真正的Opus编码器
import opuslib
encoder = opuslib.Encoder(16000, 1, opuslib.APPLICATION_AUDIO)
opus_data = encoder.encode(pcm_data, 320)
```

### 2. 实时麦克风输入
```python
# 安装 pyaudio
pip install pyaudio

# 从麦克风获取实时音频
import pyaudio
audio = pyaudio.PyAudio()
stream = audio.open(format=pyaudio.paInt16, channels=1, rate=16000, input=True)
```

### 3. 多种音频格式支持
```python
# 安装 pydub
pip install pydub

# 支持MP3、AAC等格式
from pydub import AudioSegment
audio = AudioSegment.from_mp3("music.mp3")
wav_data = audio.export(format="wav")
```

## 注意事项

1. **音频质量**: 当前使用简化编码，音质有限
2. **网络要求**: 需要稳定的WiFi连接
3. **延迟**: 实时性取决于网络延迟
4. **兼容性**: 仅支持标准WAV文件格式

## 下一步

1. 测试您的WAV文件播放
2. 根据需要调整音频参数
3. 考虑集成真正的Opus编码器
4. 添加音频控制功能（暂停、音量等）

现在您可以享受通过WebSocket播放本地WAV文件到ESP32的功能了！
