#!/usr/bin/env python3
"""
WebSocket音频服务器 - 支持WAV文件播放
用于向ESP32客户端发送音频数据
"""

import asyncio
import websockets
import struct
import time
import os
import wave
import numpy as np
import threading
from queue import Queue

class WebSocketAudioServer:
    def __init__(self, wav_file=None):
        self.seq_num = 0
        self.client_count = 0
        self.wav_file = wav_file
        self.audio_queue = Queue()
        self.audio_thread = None
        self.is_playing = False

        # 如果指定了WAV文件，启动音频处理线程
        if wav_file and os.path.exists(wav_file):
            self.load_wav_file()
            self.start_audio_thread()
        
    async def handle_client(self, websocket, path):
        self.client_count += 1
        client_addr = websocket.remote_address
        print(f"[{time.strftime('%H:%M:%S')}] Client {self.client_count} connected: {client_addr}")
        
        try:
            # 发送音频流
            while True:
                # 获取音频数据（从WAV文件或模拟数据）
                opus_data = self.get_audio_data()

                if opus_data is None:
                    # 没有更多音频数据，等待或重新开始
                    if self.wav_file:
                        print(f"[{time.strftime('%H:%M:%S')}] WAV file finished, restarting...")
                        self.restart_audio()
                        continue
                    else:
                        opus_data = self.get_simulated_opus_data()

                # 创建WebSocket音频包
                packet = self.create_audio_packet(opus_data)

                # 发送二进制数据
                await websocket.send(packet)

                self.seq_num += 1
                if self.seq_num % 50 == 0:  # 每秒打印一次状态（50帧 * 20ms = 1秒）
                    print(f"[{time.strftime('%H:%M:%S')}] Sent {self.seq_num} audio packets to {client_addr}")

                # 20ms间隔，模拟实时音频流
                await asyncio.sleep(0.02)
                
        except websockets.exceptions.ConnectionClosed:
            print(f"[{time.strftime('%H:%M:%S')}] Client disconnected: {client_addr}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] Error with client {client_addr}: {e}")
        finally:
            self.client_count -= 1
    
    def create_audio_packet(self, opus_data):
        """创建WebSocket音频包"""
        version = 0x01
        audio_type = 0x01
        payload_len = len(opus_data)
        reserved = 0x00
        
        # 打包头部（小端序，与ESP32一致）
        header = struct.pack('<BBHHH', 
                           version,        # version
                           audio_type,     # type
                           self.seq_num & 0xFFFF,  # seqNum
                           payload_len,    # payloadLen
                           reserved)       # resv
        
        return header + opus_data
    
    def load_wav_file(self):
        """加载WAV文件"""
        try:
            with wave.open(self.wav_file, 'rb') as wav:
                self.sample_rate = wav.getframerate()
                self.channels = wav.getnchannels()
                self.sample_width = wav.getsampwidth()

                print(f"WAV文件信息:")
                print(f"  文件: {self.wav_file}")
                print(f"  采样率: {self.sample_rate} Hz")
                print(f"  声道数: {self.channels}")
                print(f"  位深: {self.sample_width * 8} bit")

                # 读取所有音频数据
                frames = wav.readframes(wav.getnframes())

                # 转换为numpy数组
                if self.sample_width == 1:
                    dtype = np.uint8
                elif self.sample_width == 2:
                    dtype = np.int16
                else:
                    dtype = np.int32

                audio_data = np.frombuffer(frames, dtype=dtype)

                # 如果是立体声，转换为单声道
                if self.channels == 2:
                    audio_data = audio_data.reshape(-1, 2)
                    audio_data = np.mean(audio_data, axis=1).astype(dtype)

                # 重采样到16kHz（如果需要）
                if self.sample_rate != 16000:
                    audio_data = self.resample_audio(audio_data, self.sample_rate, 16000)

                self.audio_data = audio_data
                self.audio_pos = 0

                print(f"  处理后: 16000 Hz, 单声道, {len(audio_data)} 采样点")
                print(f"  时长: {len(audio_data) / 16000:.2f} 秒")

        except Exception as e:
            print(f"加载WAV文件失败: {e}")
            self.wav_file = None

    def resample_audio(self, audio_data, orig_sr, target_sr):
        """简单的重采样（线性插值）"""
        if orig_sr == target_sr:
            return audio_data

        # 计算重采样比例
        ratio = target_sr / orig_sr
        new_length = int(len(audio_data) * ratio)

        # 线性插值重采样
        old_indices = np.linspace(0, len(audio_data) - 1, new_length)
        new_audio = np.interp(old_indices, np.arange(len(audio_data)), audio_data)

        return new_audio.astype(audio_data.dtype)

    def start_audio_thread(self):
        """启动音频处理线程"""
        self.is_playing = True
        self.audio_thread = threading.Thread(target=self.audio_worker)
        self.audio_thread.daemon = True
        self.audio_thread.start()

    def audio_worker(self):
        """音频处理工作线程"""
        frame_size = 320  # 16kHz * 0.02s = 320 samples per 20ms frame

        while self.is_playing and hasattr(self, 'audio_data'):
            if self.audio_pos + frame_size >= len(self.audio_data):
                # 音频播放完毕，重新开始
                self.audio_pos = 0

            # 获取一帧音频数据
            frame = self.audio_data[self.audio_pos:self.audio_pos + frame_size]
            self.audio_pos += frame_size

            # 转换为PCM字节数据
            pcm_bytes = frame.astype(np.int16).tobytes()

            # 简单的"伪Opus"编码（实际应该使用真正的Opus编码器）
            # 这里我们直接发送PCM数据，ESP32端的Opus解码器会尝试处理
            opus_data = self.encode_to_pseudo_opus(pcm_bytes)

            # 放入队列
            self.audio_queue.put(opus_data)

            # 20ms间隔
            time.sleep(0.02)

    def encode_to_pseudo_opus(self, pcm_data):
        """将PCM数据编码为伪Opus格式"""
        # 注意：这不是真正的Opus编码，只是为了演示
        # 实际应用中应该使用真正的Opus编码器

        # 简单压缩：每4个字节取2个字节（降低数据量）
        compressed = pcm_data[::2]  # 简单的降采样

        # 添加简单的"Opus"头部标识
        opus_header = b'\xFC\x00'  # 伪Opus标识

        return opus_header + compressed[:60]  # 限制在60字节以内

    def get_audio_data(self):
        """获取音频数据"""
        if self.wav_file and not self.audio_queue.empty():
            return self.audio_queue.get()
        return None

    def restart_audio(self):
        """重新开始音频播放"""
        if hasattr(self, 'audio_data'):
            self.audio_pos = 0

    def get_simulated_opus_data(self):
        """获取模拟的Opus数据（当没有WAV文件时使用）"""
        frame_size = 40 + (self.seq_num % 60)  # 40-100字节变化
        opus_data = bytearray(frame_size)

        if frame_size > 0:
            opus_data[0] = 0xFC  # Opus帧开始标志（模拟）
        if frame_size > 1:
            opus_data[1] = frame_size & 0xFF

        for i in range(2, frame_size):
            opus_data[i] = (self.seq_num + i) & 0xFF

        return bytes(opus_data)

async def main():
    import sys

    # 检查命令行参数
    wav_file = None
    if len(sys.argv) > 1:
        wav_file = sys.argv[1]
        if not os.path.exists(wav_file):
            print(f"错误: WAV文件不存在: {wav_file}")
            return

    # 创建服务器实例
    server = WebSocketAudioServer(wav_file)

    # 服务器配置（与ESP32配置一致）
    host = "0.0.0.0"  # 监听所有接口
    port = 8766       # 端口号（与ESP32配置一致）

    print("=" * 60)
    print("WebSocket音频服务器启动")
    print(f"监听地址: ws://{host}:{port}")
    print(f"启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    if wav_file:
        print(f"音频文件: {wav_file}")
    else:
        print("音频模式: 模拟数据")
    print("=" * 60)
    print("等待ESP32客户端连接...")
    print("按 Ctrl+C 停止服务器")
    print()

    try:
        # 启动WebSocket服务器
        async with websockets.serve(server.handle_client, host, port):
            await asyncio.Future()  # 永远运行
    except KeyboardInterrupt:
        print("\n服务器停止")
        server.is_playing = False
    except Exception as e:
        print(f"服务器错误: {e}")
        server.is_playing = False

if __name__ == "__main__":
    # 检查依赖
    try:
        import websockets
        import numpy as np
    except ImportError as e:
        print("错误: 缺少必要的库")
        print("请运行以下命令安装依赖:")
        print("pip install websockets numpy")
        print(f"具体错误: {e}")
        exit(1)

    # 显示使用说明
    if len(sys.argv) == 1:
        print("使用方法:")
        print("  python3 websocket_audio_server.py [WAV文件路径]")
        print("")
        print("示例:")
        print("  python3 websocket_audio_server.py audio.wav")
        print("  python3 websocket_audio_server.py  # 使用模拟音频数据")
        print("")

    # 运行服务器
    asyncio.run(main())
