#!/usr/bin/env python3
"""
WebSocket音频服务器示例
用于向ESP32客户端发送音频数据
"""

import asyncio
import websockets
import struct
import time
import os

class WebSocketAudioServer:
    def __init__(self):
        self.seq_num = 0
        self.client_count = 0
        
    async def handle_client(self, websocket, path):
        self.client_count += 1
        client_addr = websocket.remote_address
        print(f"[{time.strftime('%H:%M:%S')}] Client {self.client_count} connected: {client_addr}")
        
        try:
            # 发送音频流
            while True:
                # 获取音频数据（这里使用模拟数据）
                opus_data = self.get_opus_audio_data()
                
                # 创建WebSocket音频包
                packet = self.create_audio_packet(opus_data)
                
                # 发送二进制数据
                await websocket.send(packet)
                
                self.seq_num += 1
                if self.seq_num % 50 == 0:  # 每秒打印一次状态（50帧 * 20ms = 1秒）
                    print(f"[{time.strftime('%H:%M:%S')}] Sent {self.seq_num} audio packets to {client_addr}")
                
                # 20ms间隔，模拟实时音频流
                await asyncio.sleep(0.02)
                
        except websockets.exceptions.ConnectionClosed:
            print(f"[{time.strftime('%H:%M:%S')}] Client disconnected: {client_addr}")
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] Error with client {client_addr}: {e}")
        finally:
            self.client_count -= 1
    
    def create_audio_packet(self, opus_data):
        """创建WebSocket音频包"""
        version = 0x01
        audio_type = 0x01
        payload_len = len(opus_data)
        reserved = 0x00
        
        # 打包头部（小端序，与ESP32一致）
        header = struct.pack('<BBHHH', 
                           version,        # version
                           audio_type,     # type
                           self.seq_num & 0xFFFF,  # seqNum
                           payload_len,    # payloadLen
                           reserved)       # resv
        
        return header + opus_data
    
    def get_opus_audio_data(self):
        """获取Opus编码音频数据"""
        # 这里返回模拟的Opus数据
        # 实际应用中，这里应该是真实的Opus编码音频数据
        
        # 模拟不同长度的音频帧（20-100字节）
        frame_size = 40 + (self.seq_num % 60)  # 40-100字节变化
        
        # 生成模拟的Opus数据（实际应该是有效的Opus帧）
        # 这里使用简单的模式数据，ESP32端会尝试解码
        opus_data = bytearray(frame_size)
        
        # 添加一些模式数据，模拟Opus帧结构
        if frame_size > 0:
            opus_data[0] = 0xFC  # Opus帧开始标志（模拟）
        if frame_size > 1:
            opus_data[1] = frame_size & 0xFF
        
        # 填充其余数据
        for i in range(2, frame_size):
            opus_data[i] = (self.seq_num + i) & 0xFF
            
        return bytes(opus_data)

async def main():
    server = WebSocketAudioServer()
    
    # 服务器配置
    host = "0.0.0.0"  # 监听所有接口
    port = 8080       # 端口号（与ESP32配置一致）
    
    print("=" * 50)
    print("WebSocket音频服务器启动")
    print(f"监听地址: ws://{host}:{port}")
    print(f"启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    print("等待ESP32客户端连接...")
    print("按 Ctrl+C 停止服务器")
    print()
    
    try:
        # 启动WebSocket服务器
        async with websockets.serve(server.handle_client, host, port):
            await asyncio.Future()  # 永远运行
    except KeyboardInterrupt:
        print("\n服务器停止")
    except Exception as e:
        print(f"服务器错误: {e}")

if __name__ == "__main__":
    # 检查依赖
    try:
        import websockets
    except ImportError:
        print("错误: 需要安装websockets库")
        print("请运行: pip install websockets")
        exit(1)
    
    # 运行服务器
    asyncio.run(main())
