# 直接PCM音频播放测试

## 🎯 新的测试方案

基于您的日志分析，虽然Opus解码器返回成功，但实际上无法处理"伪Opus"数据。现在我们采用**直接PCM播放**的方案来绕过Opus解码器。

## 🔧 修改内容

### ESP32端修改
- 保留Opus解码尝试
- 添加直接PCM数据播放
- 使用`SkPlayerWrite()`直接写入音频数据

### 服务器端修改
- 生成真正的1kHz正弦波PCM数据
- 16-bit, 16kHz采样率
- 320个采样点/帧 (20ms)

## 📋 测试步骤

### 1. 停止当前服务器
按Ctrl+C停止当前的WebSocket服务器

### 2. 启动新的简化测试服务器
```bash
python3 simple_audio_test_server.py
```

### 3. 重新编译ESP32
```bash
cd /opt/Amor/work/sk-terminal
idf.py build flash monitor
```

### 4. 观察新的日志输出

## 🔍 预期日志输出

### 正常情况下应该看到：

```
I (xxxxx) WsAudio: === CALLBACK TRIGGERED #1 ===
I (xxxxx) WsAudio: Received data: len=648, ptr=0x3fcxxxxx
I (xxxxx) WsAudio: Raw data: 01 01 01 00 80 02 00 00
I (xxxxx) WsAudio: Packet header: ver=1, type=1, seq=1, payloadLen=640
I (xxxxx) WsAudio: Audio data: 00 00 A1 0F...
I (xxxxx) WsAudio: Opus handler: 0x3fcae9b4
I (xxxxx) WsAudio: SkOpusDecPlayRemote returned: 0
I (xxxxx) WsAudio: ✅ Audio decode SUCCESS: seq=1, len=640
I (xxxxx) WsAudio: Attempting direct PCM playback...
I (xxxxx) WsAudio: Direct PCM write result: 640, len=640
I (xxxxx) WsAudio: 🔊 Direct PCM playback SUCCESS: 640 bytes written
```

### 关键指标：

1. **序列号递增** - seq应该从1开始递增
2. **PCM数据格式** - payloadLen=640 (320采样点 * 2字节)
3. **直接播放成功** - "Direct PCM playback SUCCESS"

## 🎵 音频特征

### 测试音频参数：
- **频率**: 1kHz正弦波
- **采样率**: 16kHz
- **位深**: 16-bit
- **声道**: 单声道
- **音量**: 中等音量 (amplitude=4000)

### 预期听到的声音：
- 清晰的1kHz纯音调
- 连续不断的正弦波音调
- 类似电话拨号音的声音

## 🔧 故障排除

### 情况1: 仍然没有声音
如果看到"Direct PCM playback SUCCESS"但仍无声音：

**可能原因**：
- 音频输出硬件问题
- I2S配置问题
- 音量设置过低

**检查方法**：
```bash
# 查看I2S初始化日志
I (xxxx) I2S_IF: STD Mode 1 bits:16/16 channel:2 sample_rate:16000

# 查看音频播放器状态
I (xxxx) SkPlayer: Write Task: start speaker
```

### 情况2: PCM写入失败
如果看到"Direct PCM playback FAILED"：

**可能原因**：
- 音频播放器未初始化
- 缓冲区满
- 数据格式不匹配

**解决方案**：
- 检查音频播放器初始化
- 调整数据大小
- 验证PCM格式

### 情况3: 序列号仍然重复
如果序列号不递增：
- 检查服务器端代码
- 重启服务器
- 检查网络连接

## 📊 服务器端输出

正常的服务器输出应该是：

```
============================================================
简化WebSocket音频测试服务器
监听地址: ws://0.0.0.0:8766
发送模式: 简单PCM测试数据
============================================================

[14:30:20] Client connected: ('192.168.3.45', 59360)
Creating packet #0: header_len=8, data_len=640, total_len=648
Generated 320 samples, 640 bytes PCM data
First few samples: 00 00 A1 0F 41 1F C0 2E

[14:30:21] Sent 50 test packets
[14:30:22] Sent 100 test packets
```

## 🎯 成功标准

测试成功的标准：
1. ✅ **序列号正确递增** (1, 2, 3, ...)
2. ✅ **PCM数据写入成功** ("Direct PCM playback SUCCESS")
3. ✅ **听到1kHz正弦波音调**

## 🔄 下一步计划

### 如果直接PCM播放成功：
- 确认音频输出正常工作
- 可以考虑集成真正的Opus编码器
- 优化音频质量和性能

### 如果仍然失败：
- 检查音频硬件配置
- 验证I2S设置
- 测试其他音频播放方法

## 💡 重要提示

这个测试绕过了Opus解码器，直接播放PCM数据。如果成功，说明：
1. WebSocket数据传输正常
2. 音频播放系统正常
3. 问题在于Opus解码器无法处理"伪Opus"数据

请立即测试并告诉我结果！特别关注是否能听到1kHz的正弦波音调。
