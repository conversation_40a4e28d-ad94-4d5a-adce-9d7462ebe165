# WebSocket音频问题诊断计划

## 🔍 当前状态分析

从您的日志可以看到：
- ✅ **WebSocket连接正常**
- ✅ **数据接收正常** (800+个包)
- ✅ **数据格式正确** (version=1, type=1)
- ✅ **解码显示成功**
- ❌ **序列号重复** (都是1886)
- ❌ **仍然没有声音**

## 🎯 问题诊断步骤

### 步骤1: 使用简化测试服务器

我创建了一个简化的测试服务器，发送简单的PCM数据而不是复杂的Opus数据：

```bash
# 停止当前服务器，启动简化测试服务器
python3 simple_audio_test_server.py
```

### 步骤2: 重新编译ESP32

```bash
cd /opt/Amor/work/sk-terminal
idf.py build flash monitor
```

### 步骤3: 观察新的调试信息

现在会显示更多信息：
- Opus解码器句柄状态
- 音频数据的前几个字节
- 详细的返回值

### 步骤4: 分析可能的问题

#### 问题A: Opus解码器未初始化
如果看到：
```
I (xxxxx) WsAudio: ❌ Opus decoder handler is NULL!
```
说明Opus解码器没有正确初始化。

#### 问题B: Opus解码器拒绝数据
如果看到：
```
I (xxxxx) WsAudio: SkOpusDecPlayRemote returned: -1
```
说明Opus解码器无法处理"伪Opus"数据。

#### 问题C: 音频播放系统问题
如果解码成功但仍无声音，可能是音频输出配置问题。

## 🔧 解决方案

### 解决方案1: 绕过Opus解码器测试

如果Opus解码器是问题，我们可以尝试直接播放PCM数据：

```c
// 在OnWsAudioData中添加直接播放测试
if (ret != SK_RET_SUCCESS) {
    SK_LOGI("WsAudio", "Trying direct PCM playback...");
    
    // 直接调用播放器播放PCM数据
    SkPlayerWrite(pkt->data, pkt->payloadLen);
}
```

### 解决方案2: 检查音频输出配置

确认音频输出是否正确配置：

```c
// 检查音频播放器状态
SK_LOGI("WsAudio", "Player status: %s", SkPlayerIsPlaying() ? "Playing" : "Stopped");
```

### 解决方案3: 使用真正的Opus编码器

如果需要真正的Opus数据：

```python
# 安装opuslib
pip install opuslib

# 修改服务器使用真正的Opus编码
import opuslib
encoder = opuslib.Encoder(16000, 1, opuslib.APPLICATION_AUDIO)
```

## 📊 预期的测试结果

### 正常情况下应该看到：

```
I (xxxxx) WsAudio: === CALLBACK TRIGGERED #1 ===
I (xxxxx) WsAudio: Received data: len=648, ptr=0x3fcxxxxx
I (xxxxx) WsAudio: Raw data: 01 01 01 00 80 02 00 00
I (xxxxx) WsAudio: Packet header: ver=1, type=1, seq=1, payloadLen=640
I (xxxxx) WsAudio: Audio data: FC 00 1F 40...
I (xxxxx) WsAudio: Opus handler: 0x3fcxxxxx
I (xxxxx) WsAudio: SkOpusDecPlayRemote returned: 0
I (xxxxx) WsAudio: ✅ Audio decode SUCCESS: seq=1, len=640
```

### 如果Opus解码器有问题：

```
I (xxxxx) WsAudio: SkOpusDecPlayRemote returned: -1
I (xxxxx) WsAudio: ❌ Audio decode FAILED: seq=1, len=640, ret=-1
I (xxxxx) WsAudio: Attempting direct audio test...
```

## 🎵 音频系统检查

### 检查音频播放器状态

在ESP32启动时，应该看到音频系统初始化：

```
I (xxxx) SkPlayer: player buffer 0x3c594adc size 1920.
I (xxxx) SkPlayer: Write Task: start speaker
I (xxxx) I2S_IF: STD Mode 1 bits:16/16 channel:2 sample_rate:16000
```

### 检查音频输出硬件

确认：
1. 扬声器是否正确连接
2. I2S配置是否正确
3. 音量设置是否合适

## 🔄 测试流程

1. **启动简化服务器**：`python3 simple_audio_test_server.py`
2. **重新编译ESP32**：`idf.py build flash monitor`
3. **观察详细日志**：特别关注Opus解码器状态
4. **根据结果调整**：选择对应的解决方案

## 💡 快速诊断

如果您想快速确定问题，请告诉我：

1. **Opus解码器句柄**是否为NULL？
2. **SkOpusDecPlayRemote返回值**是什么？
3. **是否看到I2S音频输出相关日志**？

根据这些信息，我可以为您提供精确的解决方案！

## 🎯 下一步

请使用简化测试服务器重新测试，并告诉我看到了什么新的日志信息。特别关注：
- Opus handler的地址
- SkOpusDecPlayRemote的返回值
- 是否有任何音频播放相关的错误信息
