/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: main.c
 * @description: 程序入口
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include "sdkconfig.h"
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include <netdb.h>            // struct addrinfo
#include <arpa/inet.h>
#include "sk_os.h"
#include "sk_board.h"
#include "sk_audio.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_sm.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sk_dfx.h"
#include "sk_log.h"
#include "sk_websocket.h"
#include "sk_test.h"
#include "sk_ota_api.h"

#define RESERVE_MEM_SIZE_PER_BLOCK (2048)

// WebSocket音频包结构（极简定义）
typedef struct __attribute__((packed)) {
    uint8_t version;        // 0x01
    uint8_t type;           // 0x01 (音频类型)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // Opus数据长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus音频数据
} WsAudioPacket;

static const char *TAG = "SmartKid";

SkSpeechMapItem g_skSpeechMap[] = {
    {SPEECH_CMD_EVENT_CHAT, "wu kong"},
    {SPEECH_CMD_EVENT_MUSIC, "yin yue"},
    {SPEECH_CMD_EVENT_CONFIG, "pei zhi"},
    {SPEECH_CMD_EVENT_CONFIG, "she zhi"},
    {SPEECH_CMD_EVENT_QUERY, "cha cha"},
    {SPEECH_CMD_EVENT_VOLUP, "sheng ying da yi dian"},
    {SPEECH_CMD_EVENT_VOLUP, "tiao gao ying liang"},
    {SPEECH_CMD_EVENT_VOLUP, "tiao da ying liang"},
    {SPEECH_CMD_EVENT_VOLUP, "da sheng yi dian"},
    {SPEECH_CMD_EVENT_VOLUP, "da sheng dian"},
    {SPEECH_CMD_EVENT_VOLUP, "zai da yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "sheng ying xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "tiao di ying liang"},
    {SPEECH_CMD_EVENT_VOLDOWN, "tiao xiao ying liang"},
    {SPEECH_CMD_EVENT_VOLDOWN, "xiao sheng yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "xiao sheng dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "zai xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLMAX, "zui da ying liang"},
    {SPEECH_CMD_EVENT_VOLMAX, "zui da sheng"},
    {SPEECH_CMD_EVENT_VOLMIN, "zui xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLMIN, "zui xiao sheng"},
    {SPEECH_CMD_EVENT_HELP, "qiu zhu"},
    {SPEECH_CMD_EVENT_PAUSE, "zan ting"},
    {SPEECH_CMD_EVENT_CONFIRM, "que ding"},
    {SPEECH_CMD_EVENT_QUIT, "tui chu"},
    {SPEECH_CMD_EVENT_QUIT, "ting zhi"},
    {SPEECH_CMD_EVENT_PREV, "shang yi ge"},
    {SPEECH_CMD_EVENT_NEXT, "xia yi ge"},
    {SPEECH_CMD_EVENT_RESUME, "ji xu"},
    {SPEECH_CMD_EVENT_QUIT, "qu xiao"},
    {SPEECH_CMD_EVENT_INFO, "zhuang tai"},
    {SPEECH_CMD_EVENT_START_DBG, "qi dong"},
    {SPEECH_CMD_EVENT_STOP_DBG, "duan kai"},
    {SPEECH_CMD_EVENT_SLEEP, "dai ji"},
    {SPEECH_CMD_EVENT_CALL, "fu jiao"},
    {SPEECH_CMD_EVENT_CALL, "hu jiao"},
    {SPEECH_CMD_EVENT_MIC_ON, "lu ying"},
    {SPEECH_CMD_EVENT_MIC_ON, "lu yin"},
    {SPEECH_CMD_EVENT_MIC_OFF, "guan bi"},
    {SPEECH_CMD_EVENT_CALL, "qi dong hu jiao"},
    {SPEECH_CMD_EVENT_CALL, "da kai dian hua"},
    {SPEECH_CMD_EVENT_CONFIG, "pei wang"},
};

SkStateHandler g_smHandler;

// WebSocket连接启动函数（在WiFi连接成功后调用）
void StartWebSocketConnection(void) {
    static bool ws_connected = false;

    if (!ws_connected) {
        SkWsStartConnect();
        ws_connected = true;
        SK_LOGI(TAG, "WebSocket connection started after WiFi ready");
    }
}

// WebSocket音频数据回调（极简实现）
void OnWsAudioData(void *arg, void *data, uint16_t len) {
    static uint32_t callbackCount = 0;
    WsAudioPacket *pkt = (WsAudioPacket *)data;
    SkAudioDownlinkTimeRecord timeRecord = {0};

    callbackCount++;

    // 添加明显的调试日志
    SK_LOGI("WsAudio", "=== CALLBACK TRIGGERED #%u ===", callbackCount);
    SK_LOGI("WsAudio", "Received data: len=%d, ptr=%p", len, data);

    if (len >= 8) {
        // 打印前8个字节的原始数据
        uint8_t *raw = (uint8_t *)data;
        SK_LOGI("WsAudio", "Raw data: %02X %02X %02X %02X %02X %02X %02X %02X",
                raw[0], raw[1], raw[2], raw[3], raw[4], raw[5], raw[6], raw[7]);
    }

    // 简单验证数据格式
    if (len < sizeof(WsAudioPacket)) {
        SK_LOGE("WsAudio", "Packet too short: len=%d, expected>=%d", len, sizeof(WsAudioPacket));
        return;
    }

    SK_LOGI("WsAudio", "Packet header: ver=%d, type=%d, seq=%d, payloadLen=%d",
            pkt->version, pkt->type, pkt->seqNum, pkt->payloadLen);

    if (pkt->version != 1 || pkt->type != 1) {
        SK_LOGE("WsAudio", "Invalid packet format: ver=%d, type=%d", pkt->version, pkt->type);
        return;
    }

    // 验证负载长度
    uint16_t expectedPayloadLen = len - sizeof(WsAudioPacket);
    if (pkt->payloadLen != expectedPayloadLen) {
        SK_LOGE("WsAudio", "Payload length mismatch: header=%d, actual=%d",
                pkt->payloadLen, expectedPayloadLen);
        return;
    }

    SK_LOGI("WsAudio", "Calling SkOpusDecPlayRemote with %d bytes...", pkt->payloadLen);

    // 构造时间戳记录
    timeRecord.decRxTick = SkOsGetTickCnt();

    // 直接调用现有的解码播放函数
    int32_t ret = SkOpusDecPlayRemote(SkOpusDecGetHandler(), 0xFFFF,
                                     pkt->data, pkt->payloadLen, &timeRecord);

    if (ret == SK_RET_SUCCESS) {
        SK_LOGI("WsAudio", "✅ Audio decode SUCCESS: seq=%d, len=%d", pkt->seqNum, pkt->payloadLen);
    } else {
        SK_LOGE("WsAudio", "❌ Audio decode FAILED: seq=%d, len=%d, ret=%d", pkt->seqNum, pkt->payloadLen, ret);
    }

    SK_LOGI("WsAudio", "=== CALLBACK END #%u ===", callbackCount);
}

void SkMainCmdProc(int32_t cmd) {
    if (g_smHandler == NULL) {
        return;
    }

    // 处理特殊命令（可选：保留手动重连功能）
    switch (cmd) {
        case SPEECH_CMD_EVENT_CONFIG:  // "配置" 或 "设置" 命令可重新连接WebSocket
            StartWebSocketConnection();
            SK_LOGI(TAG, "WebSocket connection triggered by voice command (manual reconnect)");
            break;
        default:
            SkSmSendEvent(g_smHandler, SM_EVENT_CMD, cmd, 0, 0);
            break;
    }
    return;
}
#ifndef TESTCASE_ENABLED
static void ReserveMemory(uint32_t **memBlock, uint32_t memBlockCnt, size_t blockSize) {
    for (int i = 0; i < memBlockCnt; i++) {
        // 不能超过配置门限，超过门限从PSRAM中分配，不能起到保留效果。所以分片分配.
        memBlock[i] = malloc(blockSize);
    }

    return;
}

static void FreeReserveMemory(uint32_t **memBlock, uint32_t memBlockCnt) {
    for (int i = 0; i < memBlockCnt; i++) {
        if (memBlock[i] != NULL) {
            free(memBlock[i]);
            memBlock[i] = NULL;
        }
    }

    return;
}
#endif

void app_main(void) {
#ifndef TESTCASE_ENABLED
    uint32_t *memResv[32];
    SkRlinkHandler rlinkHandler;
#endif
    SK_LOGI(TAG, "Debug version at %s %s.", __DATE__, __TIME__);
    SkRledInit();
    SkRledSetEvent(SK_LED_EVENT_INIT);
    SK_OS_MODULE_MEM_STAT("start", false);
#ifndef TESTCASE_ENABLED
    ReserveMemory(memResv, ARRAY_SIZE(memResv), RESERVE_MEM_SIZE_PER_BLOCK);
    SK_OS_MODULE_MEM_STAT("Resv", true);
#endif
    ESP_ERROR_CHECK(SkBspBoardInit(16000, sizeof(uint16_t) * 8));
    SK_OS_MODULE_MEM_STAT("Bsp", true);
#ifndef TESTCASE_ENABLED
    g_smHandler = SkSmInit();
    SkConfigInit();
    SK_OS_MODULE_MEM_STAT("Config", true);
    SkWifiInit();
    SkWifiRegEventCb(SkSmOnWifiEvent);
    SK_OS_MODULE_MEM_STAT("WiFiTask", true);
    SkOtaManagerInit();
    SkOtaRegStateCallback(SkSmOtaOnStateChange);
    SK_OS_MODULE_MEM_STAT("OTA", true);
    SkOpusInit(16000, 1, 60);
    SK_OS_MODULE_MEM_STAT("OpusCodec", true);
    SkSrRegister(g_skSpeechMap, sizeof(g_skSpeechMap) / sizeof(SkSpeechMapItem), SkMainCmdProc);
    SK_OS_MODULE_MEM_STAT("Clink", false);
    SkClinkInit(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Clink", true);
    rlinkHandler = SkRlinkInit(g_smHandler, sizeof(uint16_t), 512, 800);
    SK_OS_MODULE_MEM_STAT("Rlink", true);
    SkWsInit();
    SkWsStart();
    // 注册WebSocket音频数据回调
    SK_LOGI(TAG, "Registering WebSocket binary data callback...");
    SkWsRegOnBinDataCallback(OnWsAudioData, NULL);
    SK_LOGI(TAG, "WebSocket binary data callback registered successfully");
    SK_OS_MODULE_MEM_STAT("WebSocket", true);
    SK_OS_MODULE_MEM_STAT("Audio", false);
    SkAudioInit(sizeof(uint16_t), 960);
    SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
    SkSrSetSendFunc(SkOpusEncEnqueue);
    SkOpusEncSetCallback(SkRlinkFeedReordAudio, (void *)rlinkHandler);
    SkRlinkSetCodedDataCallback(SkOpusDecPlayRemote, SkOpusDecGetHandler());
    SkRlinkSetCodedDataEndCallback(SkOpusDecRemoteDataEnd, SkOpusDecGetHandler());
    SkPeripheralInit(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Peripheral", true);
    FreeReserveMemory(memResv, ARRAY_SIZE(memResv));
    SK_OS_MODULE_MEM_STAT("Resv-End", true);

    // 配置WebSocket音频服务器（但不立即连接，等待WiFi连接成功）
    SkWsSetServerIp("************", 8766);
    SK_LOGI(TAG, "WebSocket audio server configured: ************:8766 (will connect after WiFi ready)");

    SK_OS_MODULE_MEM_STAT("WifiSta", false);
    SkSmSendEvent(g_smHandler, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_INIT_OK, 0, 0);

    // 等待WiFi连接稳定
    vTaskDelay(pdMS_TO_TICKS(15000));
    SK_OS_MODULE_MEM_STAT("WifiSta", true);

    // 自动启动WebSocket连接
    SK_LOGI(TAG, "Auto-starting WebSocket connection...");
    StartWebSocketConnection();
#else
    SkTestMain();
#endif
    return;
}
