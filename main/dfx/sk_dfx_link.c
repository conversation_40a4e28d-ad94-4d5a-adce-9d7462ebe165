/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_dfx_server.c
 * @description: DFX功能的服务端(Dfx link). 功能如下:
 *               1) 对外客户端通过网络接入服务端: 下发配置, 读取参数;
 *               2) 对内提供接口, 将数据(如音频\传感器状态\时延信息)上报给客户端；
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <sys/socket.h>
#include <errno.h>
#include <netdb.h>            // struct addrinfo
#include <arpa/inet.h>
#include "sk_dfx.h"
#include "sk_board.h"
#include "sk_frame.h"
#include "sk_common.h"
#include "sk_log.h"
#include "sk_config.h"
#include "sk_os.h"
#include "sk_rlink.h"
#include "sk_audio.h"
#include "sk_audio_buffer.h"
#include "sk_queue.h"
#include "sk_buf_pool.h"

#define TAG "SkDfxLink"

#define DLINK_RX_BUF_SIZE 1024

enum {
    PARAM_ID_SYS_CFG = 0,
    PARAM_ID_SPK_VOL = 1,
    PARAM_ID_MIC_VOL = 2,
    PARAM_ID_FRIEND_LIST = 3,
    PARAM_ID_MUSIC_LIST = 4,
    PARAM_ID_STORY_LIST = 5,
    PARAM_ID_WIFI_CFG = 6,
    PARAM_ID_MAX
};

typedef struct {
    uint32_t audioCnt;
    uint32_t lostCnt;
    uint32_t runFlag;
    uint8_t *rxBuf;

    int sockReady;
    int sock;           // socket for client.
    int serverSock;     // socket for accept.
    uint32_t failCnt;
    uint32_t sendCnt;
    uint32_t timeoutCnt;
    uint32_t audioReport;

    SkAudioQueueHandler txQueue;
    SemaphoreHandle_t doneSem;
    SkBufPool txAudioArray;
} SkDfxLink_t;

// 主任务
void SkDfxLinkRxTask(void *arg);
// 从发送队列获取数据发送
void SkDfxLinkTxTask(void *arg);
// 资源分配
static int32_t SkDfxLinkInit(SkDfxLink_t *ctrl);
// 资源回收
static void SkDfxLinkCleanup(SkDfxLink_t *ctrl);
// 启动Server, 等待Client;
int32_t SkDfxLinkConnect(SkDfxLink_t *ctrl, uint16_t port);
// 关闭连接和Server.
void SkDfxLinkDisconnect(SkDfxLink_t *ctrl);
// 发送消息登录信息
int32_t SkDbgSendLogin(SkDfxLink_t *ctrl);
// 接收消息的处理
uint16_t SkDfxLinkProcMsg(SkDfxLink_t *ctrl, uint8_t *buffer, uint32_t bytes);
// 入发送队列前打包
int32_t SkDfxLinkPacketData(uint16_t msgType, uint8_t *dstBuf, 
    uint32_t dstLen, uint8_t *srcBuf, uint32_t bytes);
// 发送数据
int32_t SkDfxLinkSendData(SkDfxLink_t *ctrl, const uint8_t *data, size_t len);
// 接收链路检查消息头是否正确
int SkDfxlinkCheckMsg(FrameHead *head);
// 消息处理: 获取参数
void SkDfxLinkGetParamCmd(uint8_t *buffer, uint32_t bytes);
// 消息处理: 设置参数
void SkDfxLinkSetParamCmd(uint8_t *buffer, uint32_t bytes);
// 上报好友列表
void SkDfxLinkRptFriendList();

SkDfxLink_t g_dlinkCtrl;
TaskHandle_t g_debugTaskHandle = NULL;

void SkDfxLinkStart() {
    if (g_debugTaskHandle != NULL) {
        return;
    }
    xTaskCreate(SkDfxLinkRxTask, "SkDfxLinkRxTask", 4096, &g_dlinkCtrl, 5, &g_debugTaskHandle);
    return;
}

void SkDfxLinkStop() {
    g_dlinkCtrl.runFlag = 0;

    return;
}

static inline bool SkDfxLinkIsReady(SkDfxLink_t *ctrl) {
    return (ctrl->sockReady == 1) ? true : false;
}

void SkDfxLinkSendAudio(uint16_t msgType, uint8_t *buffer, uint32_t bytes) {
    int32_t ret;
    SkDfxLink_t *ctrl = &g_dlinkCtrl;

    if (!SkDfxLinkIsReady(ctrl)) {
        return;
    }
    if (ctrl->audioReport == 0) {
            return;
    }
    ctrl->audioCnt++;
    SkBufPoolNode *sendBuf = SkBufPoolGetFree(&ctrl->txAudioArray);
    if (sendBuf == NULL) {
        ctrl->lostCnt++;
        return;
    }
    ret = SkDfxLinkPacketData(msgType, sendBuf->data, sendBuf->size, buffer, bytes);
    // 如果打包失败，返回0, 在处理时直接回收，这个线程不能进行回收.
    sendBuf->head = 0;
    sendBuf->tail = ret;
    SkBufPoolPutData(&ctrl->txAudioArray, sendBuf);

    return;
}

void SkDfxLinkSendMsg(uint16_t msgType, uint8_t *buffer, uint32_t bytes) {
    int32_t ret;
    SkDfxLink_t *ctrl = &g_dlinkCtrl;

    if (!SkDfxLinkIsReady(ctrl)) {
        return;
    }

    SkAudioBuf *sendBuf = SkAudioBufferGetFree(ctrl->txQueue, 0);
    if (sendBuf == NULL) {
        return;
    }
    ret = SkDfxLinkPacketData(msgType, sendBuf->data, sendBuf->length, buffer, bytes);
    if (ret > 0) {
        sendBuf->length = ret;
        SkAudioBufferPutData(ctrl->txQueue, sendBuf);
    } else {
        SkAudioBufferPutFree(ctrl->txQueue, sendBuf);
    }

    return;
}

void SkDfxLinkRxTask(void *arg) {
    SkDfxLink_t *ctrl = (SkDfxLink_t *)arg;
    uint16_t procLen, lastBytes;
    int32_t bytes;
    int32_t ret;

    ret = SkDfxLinkInit(ctrl);
    if (ret != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "Debug task init failed!");
        SkDfxLinkCleanup(ctrl);
        vTaskDelete(NULL);
        return;
    }

    if (SkDfxLinkConnect(ctrl, 9526) != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "Debug task connect failed!");
        SkDfxLinkCleanup(ctrl);
        vTaskDelete(NULL);
        return;
    }

    xTaskCreate(SkDfxLinkTxTask, "SkDfxLinkTxTask", 4096, ctrl, 5, NULL);
    lastBytes = 0;
    SK_LOGI(TAG, "SkDfxLinkRxTask start!");
    while (ctrl->runFlag == 1) {
        bytes = recv(ctrl->sock, &ctrl->rxBuf[lastBytes], 1024 - lastBytes, 0);
        if (bytes <= 0) {
            // 连接已断开或异常
            ctrl->timeoutCnt++;
            if (ctrl->timeoutCnt >= 400) {
                break;
            } else {
                continue;
            }
        }

        lastBytes += bytes;
        if (lastBytes < sizeof(FrameHead)) {
            continue;
        }
        
        procLen = SkDfxLinkProcMsg(ctrl, ctrl->rxBuf, lastBytes);
        if (procLen == 0) {
            continue;
        }
        if (lastBytes >= procLen) {
            lastBytes = 0;
            continue;
        }
        lastBytes -= procLen;
        if (lastBytes > procLen) {
            // 剩余数据超过处理的数据，往前移动时直接memcpy可能会出错，逐个拷贝
            for (int i = 0; i < lastBytes; i++) {
                ctrl->rxBuf[i] = ctrl->rxBuf[i + procLen];
            }
        } else {
            memcpy(ctrl->rxBuf, ctrl->rxBuf + procLen, lastBytes);
        }
    }
    SkDfxLinkDisconnect(ctrl);
    xSemaphoreTake(ctrl->doneSem, portMAX_DELAY);
    SK_LOGI(TAG, "SkDfxLinkRxTask exit %u %u!", ctrl->audioCnt, ctrl->lostCnt);
    SkDfxLinkCleanup(ctrl);
    vTaskDelete(NULL);
}

void SkDfxLinkTxTask(void *arg) {
    SkDfxLink_t *ctrl = (SkDfxLink_t *)arg;
    SkAudioBuf *queueBuf = NULL;
    SkBufPoolNode *node = NULL;
    uint16_t timeoutCnt;

    SkDbgSendLogin(ctrl);
    while (ctrl->runFlag == 1) {
        if (!SkDfxLinkIsReady(ctrl)) {
            break;
        }
        node = SkBufPoolGetData(&ctrl->txAudioArray);
        if (node != NULL) {
            SkDfxLinkSendData(ctrl, &node->data[node->head], node->tail - node->head);
            SkBufPoolPutFree(&ctrl->txAudioArray, node);
            timeoutCnt = 0;
        } else {
            timeoutCnt = 20;
        }
        queueBuf = SkAudioBufferGetData(ctrl->txQueue, timeoutCnt);
        if (queueBuf != NULL) {
            SkDfxLinkSendData(ctrl, queueBuf->data, queueBuf->length);
            SkAudioBufferPutFree(ctrl->txQueue, queueBuf);
        }
    }
    xSemaphoreGive(ctrl->doneSem);
    vTaskDelete(NULL);
}

static int32_t SkDfxLinkInit(SkDfxLink_t *ctrl) {
    int32_t ret;

    ctrl->audioCnt = 0;
    ctrl->lostCnt = 0;
    ctrl->runFlag = 1;
    ctrl->timeoutCnt = 0;
    ctrl->audioReport = 0;
    ctrl->doneSem = xSemaphoreCreateBinary();
    if (ctrl->doneSem == NULL) {
        return SK_RET_FAIL;
    }
    ret = SkBufPoolInit(&ctrl->txAudioArray, 256, 2048 + 32, 32, 32);
    if (ret != SK_RET_SUCCESS) {
        return ret;
    }
    ctrl->txQueue = SkCreateAudioQueue(32, 1024, sizeof(FrameHead));
    if (ctrl->txQueue == NULL) {
        return SK_RET_FAIL;
    }
    ctrl->rxBuf = malloc(DLINK_RX_BUF_SIZE);
    if (ctrl->rxBuf == NULL) {
        return SK_RET_FAIL;
    }
    SK_LOGI(TAG, "SkDfxLinkInit done");

    return SK_RET_SUCCESS;
}

static void SkDfxLinkCleanup(SkDfxLink_t *ctrl) {
    SkAudioQueueHandler txQueue = ctrl->txQueue;
    ctrl->txQueue = NULL;
    if (txQueue != NULL) {
        SkDesctoryAudioQueue(txQueue);
    }
    if (ctrl->rxBuf != NULL) {
        free(ctrl->rxBuf);
        ctrl->rxBuf = NULL;
    }
    if (ctrl->doneSem != NULL) {
        vSemaphoreDelete(ctrl->doneSem);
        ctrl->doneSem = NULL;
    }
    SkBufPoolDeinit(&ctrl->txAudioArray);
    g_debugTaskHandle = NULL;
}

int32_t SkDfxLinkConnect(SkDfxLink_t *ctrl, uint16_t port) {
    int ret, sock, newSock;
    struct sockaddr_in dbgAddr;
    socklen_t addrLen = sizeof(struct sockaddr_in);

    if (ctrl->sockReady == 1) {
        return SK_RET_FAIL;
    }

    memset(&dbgAddr, 0, sizeof(struct sockaddr_in));
    dbgAddr.sin_family = AF_INET;
    sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);

    SK_LOGI(TAG, "Start debug server %p", sock);
    dbgAddr.sin_addr.s_addr = INADDR_ANY; // 使用当前的IP地址
    dbgAddr.sin_port = htons(port);

    ret = bind(sock, (struct sockaddr *)&dbgAddr, sizeof(struct sockaddr_in));
    if (ret != 0) {
        SK_LOGI(TAG, "bind failed! socket num=%d ret=%d", sock, ret);
        close(sock);
        return SK_RET_FAIL;
    }

    ret = listen(sock, 1); // 设置backlog为1，每次只接收一个连接
    if (ret != 0) {
        SK_LOGI(TAG, "listen failed! socket num=%d ret=%d", sock, ret);
        close(sock);
        return SK_RET_FAIL;
    }
    ctrl->serverSock = sock;
    newSock = accept(sock, (struct sockaddr *)&dbgAddr, &addrLen);
    if (newSock < 0) {
        SK_LOGI(TAG, "accept failed! socket num=%d ret=%d", sock, newSock);
        close(sock);
        return SK_RET_FAIL;
    }

    ctrl->sockReady = 1;
    ctrl->sock = newSock;
    SK_LOGI(TAG, "Debug server started and accepted a connection");

    return SK_RET_SUCCESS;
}

void SkDfxLinkDisconnect(SkDfxLink_t *ctrl) {
	if (ctrl->sockReady == 1) {
		ctrl->sockReady = 0;
        SK_LOGI(TAG, "Close socket to debug server %p", ctrl->sock);
	    closesocket(ctrl->sock);
		ctrl->sock = -1;
        close(ctrl->serverSock);
        ctrl->serverSock = -1;
	}
}

int32_t SkDfxLinkPacketData(uint16_t msgType, uint8_t *dstBuf, uint32_t dstLen, uint8_t *srcBuf, uint32_t bytes) {
    uint16_t payloadLen;
    FrameHead *head = (FrameHead *)dstBuf;

    if (msgType == MSG_DFX_AND_TERM_KEEPALIVE) {
        payloadLen = 0;
    } else if (msgType == MSG_DFX_AND_TERM_GET_PARAM) {
        payloadLen = sizeof(DbgParamData) + 800;
    } else {
        payloadLen = bytes;
    }
    if (payloadLen != 0) {
        if ((srcBuf == NULL) || (bytes != payloadLen) || 
            ((payloadLen + sizeof(FrameHead)) > dstLen)) {
            return 0;
        }
        memcpy(&dstBuf[sizeof(FrameHead)], srcBuf, payloadLen);
    }

    EncodeFrameHead(head, FRAME_CMSG_DFX_AND_TERM, msgType, payloadLen);
    return payloadLen + sizeof(FrameHead);
}

int32_t SkDbgSendLogin(SkDfxLink_t *ctrl) {
    uint8_t data[sizeof(SkBoardDateTime) + sizeof(FrameHead)];
    FrameHead *head = (FrameHead *)data;
    SkBoardDateTime *dateTime = (SkBoardDateTime *)(&data[sizeof(FrameHead)]);

    SkOsGetTimeInfo(&dateTime->sec, &dateTime->ms, &dateTime->tickCnt);
    SK_LOGI(TAG, "DFX start at %llu.%03d tickCnt:%u",
        dateTime->sec, dateTime->ms, dateTime->tickCnt);
    EncodeFrameHead(head, FRAME_CMSG_DFX_AND_TERM, MSG_DFX_AND_TERM_DATE_INFO, sizeof(SkBoardDateTime));

    return SkDfxLinkSendData(ctrl, data, sizeof(SkBoardDateTime) + sizeof(FrameHead));
}

int32_t SkDfxLinkSendData(SkDfxLink_t *ctrl, const uint8_t *data, size_t len) {
    int32_t ret;

    if (ctrl->sockReady == 0) {
        return SK_RET_FAIL;
    }

    ret = send(ctrl->sock, data, len, 0);
    if (ret < 0) {
        ctrl->failCnt++;
        ret = SK_RET_FAIL;
    } else {
        ctrl->sendCnt++;
        ret = SK_RET_SUCCESS;
    }

    return ret;
}

uint16_t SkDfxLinkProcMsg(SkDfxLink_t *ctrl, uint8_t *buffer, uint32_t bytes) {
    uint16_t procLen, frameLen;

    procLen = 0;
    while (bytes >= sizeof(FrameHead)) {
        FrameHead *head = (FrameHead *)buffer;
        if (SkDfxlinkCheckMsg(head) != SK_RET_SUCCESS) {
            SK_LOGE(TAG, "SkDfxlinkCheckMsg failed!");
            SkOsShowArray(buffer, bytes);
            procLen = bytes;
            break;
        }
        head->payloadLen = ntohs(head->payloadLen);
        if (head->payloadLen > bytes - sizeof(FrameHead)) {
            SK_LOGE(TAG, "Data not complete %d %d!", head->payloadLen, bytes - sizeof(FrameHead));
            break;
        }
        SK_LOGD(TAG, "Rx msg type %d!", head->msgType);
        switch (head->msgType) {
            case MSG_DFX_AND_TERM_GET_PARAM:
                SkDfxLinkGetParamCmd(&buffer[sizeof(FrameHead)], head->payloadLen);
                break;
            case MSG_DFX_AND_TERM_SET_PARAM:
                SkDfxLinkSetParamCmd(&buffer[sizeof(FrameHead)], head->payloadLen);
                break;
            case MSG_DFX_AND_TERM_KEEPALIVE:
                ctrl->timeoutCnt = 0;
                SkDfxLinkSendMsg(MSG_DFX_AND_TERM_KEEPALIVE, NULL, 0);
                break;
            case MSG_DFX_TO_TERM_ENABLE_REPORT_DEVINFO:
                SkPeripheralEnableReport(true);
                break;
            case MSG_DFX_TO_TERM_ENABLE_REPORT_TIME_RECORD:
                SkPlayerEnableReport(true);
                break;
            case MSG_DFX_TO_TERM_DISABLE_REPORT_DEVINFO:
                SkPeripheralEnableReport(false);
                break;
            case MSG_DFX_TO_TERM_DISABLE_REPORT_TIME_RECORD:
                SkPlayerEnableReport(false);
                break;
            case MSG_DFX_TO_TERM_ENABLE_REPORT_AUDIO:
                ctrl->audioReport = 1;
                break;
            case MSG_DFX_TO_TERM_DISABLE_REPORT_AUDIO:
                ctrl->audioReport = 0;
                break;
            case MSG_DFX_TO_TERM_RESET_GYRO:
                SkPeripheralResetGyro();
                break;
            default:
                SK_LOGE(TAG, "Unknown frame type %d", head->frameType);
        }
        frameLen = sizeof(FrameHead) + head->payloadLen;
        procLen += frameLen;
        bytes -= frameLen;
        buffer += frameLen;
    }

    return procLen;
}

int SkDfxlinkCheckMsg(FrameHead *head) {
    if (ntohs(head->headFlag) != FRAME_HEAD_FLAG ||
        head->frameType != FRAME_CMSG_DFX_AND_TERM ||
        ntohs(head->headLen) != sizeof(FrameHead)) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

void SkDfxLinkGetParamCmd(uint8_t *buffer, uint32_t bytes) {
    DbgParamData *param = (DbgParamData *)buffer;

    if (param->paramId >= PARAM_ID_MAX) {
        SK_LOGI(TAG, "Param %d not supported yet!", param->paramId);
        return;
    }

    if (param->paramId == PARAM_ID_FRIEND_LIST) {
        SK_LOGI(TAG, "Get friends list.");
        SkDfxLinkRptFriendList();        
    } else {
        SK_LOGI(TAG, "Param %d not supported yet!", param->paramId);
    }
    return;
}

void SkDfxLinkSetParamCmd(uint8_t *buffer, uint32_t bytes) {
    DbgParamData *param = (DbgParamData *)buffer;
    if (param->paramId >= PARAM_ID_MAX) {
        return;
    }

    if (param->paramId == PARAM_ID_SYS_CFG) {
        DbgSysConfig *cfg = (DbgSysConfig *)param->data;
        SkConfigSetCtrlServer((char *)cfg->ctrlServer);
        cfg->ctrlServer[sizeof(cfg->ctrlServer) - 1] = '\0';
        SkConfigSetCtrlServerPort(cfg->port);
        SkConfigSetSpkInitVol(cfg->spkInitVol);
        SkConfigSetMicInitVol(cfg->micInitVol);
        SkConfigSave();
        SkRlinkSetCallAgent((char *)cfg->callAgentIp, cfg->callAgentPort);
        SK_LOGI(TAG, "Set ctrl server %s:%d, spk vol %d, mic vol %d",
            cfg->ctrlServer, cfg->port, cfg->spkInitVol, cfg->micInitVol);
        SkSrSetMnThd(cfg->mnThd);
    } else if (param->paramId == PARAM_ID_SPK_VOL) {
        SkBspSetPlayVol(param->data[0]);
        SK_LOGI(TAG, "Set spk vol %d", param->data[0]);
    } else if (param->paramId == PARAM_ID_MIC_VOL) {
        SkBspSetMicVol(param->data[0]);
        SK_LOGI(TAG, "Set mic vol %d", param->data[0]);
    } else if (param->paramId == PARAM_ID_FRIEND_LIST) {
        SkConfigSetCalleeList(param->data, param->length);
        SkConfigSave();
    } else if (param->paramId == PARAM_ID_MUSIC_LIST) {
    } else if (param->paramId == PARAM_ID_STORY_LIST) {
    } else if (param->paramId == PARAM_ID_WIFI_CFG) {
        SkConfigDelSsid();
    }
    return;
}

void SkDfxLinkRptFriendList() {
    uint8_t *rptBuffer = NULL;
    DbgParamData *param = NULL;

    rptBuffer = SkOsAllocPsram(sizeof(DbgParamData) + 800, sizeof(uint32_t));
    if (rptBuffer == NULL) {
        return;
    }
    param = (DbgParamData *)rptBuffer;
    param->paramId = PARAM_ID_FRIEND_LIST;
    param->resv = 0;
    param->length = 800;
    SkConfigGetCalleeList(param->data, 800);
    SkDfxLinkSendMsg(MSG_DFX_AND_TERM_GET_PARAM, rptBuffer, sizeof(DbgParamData) + 800);
    free(rptBuffer);
    rptBuffer = NULL;
    param = NULL;

    return;
}
